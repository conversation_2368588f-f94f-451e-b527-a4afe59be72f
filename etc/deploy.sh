if [ "$1" = 'p' ]; then
  npm version patch
elif [ "$1" = 'm' ]; then
  npm version minor
elif [ "$1" = 'M' ]; then
  npm version major
else
  echo 'Please input version type: p, m, M'
  exit 1
fi

sh ./etc/build.sh

PACKAGE_VERSION=$(cat package.json \
  | grep version \
  | head -1 \
  | awk -F: '{ print $2 }' \
  | sed 's/[", ]//g')

docker build --no-cache -t statistic-service:$PACKAGE_VERSION -t statistic-service:latest .

docker images statistic-service

DEPLOYING_PATH=../deployer/release/deploying
mkdir -p $DEPLOYING_PATH

docker save statistic-service > $DEPLOYING_PATH/statistic-service.tar

RELEASE_FILE=$DEPLOYING_PATH/RELEASE.txt
CURR_TAG=$(git log --tags --simplify-by-decoration --pretty="format:%d" | grep -o 'v[0-9.]\+' | sed -n '1p')
PREV_TAG=$(git log --tags --simplify-by-decoration --pretty="format:%d" | grep -o 'v[0-9.]\+' | sed -n '2p')
echo "statistic-service:v$PACKAGE_VERSION\n" >> $RELEASE_FILE
git log $PREV_TAG..$CURR_TAG --oneline | cut -c 9- | sed 's/^/- /' >> $RELEASE_FILE
echo "\n\n" >> $RELEASE_FILE


rm -rf dist

git push origin "v$PACKAGE_VERSION"
git push origin main
