# Statistic-Service hand on

### Load the docker image.

```sh
docker load < statistic-service.tar
```

### Check the image

```sh
docker images statistic-service
```

### Start the service

```sh
docker run
-e DB_TYPE=pg \
-e DB_URL=postgresql://${cockroachDB account}:${CockroachDB password}@${cockroachDB ip}:${cockroachDB port}/${cockroachDB database} \
-e INFLUXDB_URL=http://${your Influxdb IP:PORT} \
-e INFLUXDB_SPIN_MEASUREMENT=${your Influxdb data measurement} \
-e INFLUXDB_TOKEN=${your Influxdb Token} \
-e INFLUXDB_ORG=${your influxdb organize} \
-e INFLUXDB_BUCKET=${your influxdb bucket} \
-e REDIS_URL=${your redis url} \

-e TIMEZONE=Asia/Jakarta \

-e MIN_STATISTICS_QUEUE_EVERY="15s" \
-e MIN_STATISTICS_BACKWARD_RANGE=60 \
-e MIN_STATISTICS_NEXT_RANGE=1 \

-e MIN5_STATISTICS_QUEUE_EVERY="15s" \
-e MIN5_STATISTICS_BACKWARD_RANGE=60 \
-e MIN5_STATISTICS_NEXT_RANGE=5 \

-e HOURLY_STATISTICS_QUEUE_EVERY="15s" \
-e HOURLY_STATISTICS_BACKWARD_RANGE=24 \
-e HOURLY_STATISTICS_NEXT_RANGE=1 \

-e DAILY_STATISTICS_QUEUE_EVERY="15s" \
-e DAILY_STATISTICS_BACKWARD_RANGE=5 \
-e DAILY_STATISTICS_NEXT_RANGE=1 \

statistic-service
```

## Environments

#### DB_TYPE

Default to postgresql `pg` which can connect to both cockroachdb and postgresql.

#### DB_URL (Require)

The full url of database. Idea format will be like `postgresql://<EMAIL>:26257/<database_name>`

#### INFLUXDB_URL (Require)

The full url of influxdb. Idea format will be like `http://host.docker.internal:8086`.

#### INFLUXDB_SPIN_MEASUREMENT (Require)

The raw data measurement of influxdb. Default is `spin-history`.

#### INFLUXDB_TOKEN (Require)

The auth token of influxdb. Default is empty, should be given.

#### INFLUXDB_ORG (Require)

The organization of influxdb. Default is `edge-slot`.

#### INFLUXDB_BUCKET (Require)

The bucket of influxdb. Default is `game-data`.

#### TIMEZONE (require)

The timezone string for local time. default is `Asia/Jakarta`.
If want to use in +8 timezone, please use `Asia/Taipei`.

#### INFLUXDB_CONNECTION_TIMEOUT (Optional)

The connection timeout of influxdb. Default is 120000 (2 minutes).

#### REDIS_URL (Require)

The redis url. Idea format will be like `redis://host.docker.internal:6379`

### Optional Environments

#### MIN_STATISTICS_QUEUE_EVERY (optional)

The frequency setting for cron job. default `15s` (process per minute.)

#### MIN_STATISTICS_BACKWARD_RANGE (optional)

Defined the range per processing backward (in minute). default 60 minutes.

#### MIN_STATISTICS_NEXT_RANGE (optional)

Defined the range per processing forward (in minute). default 1 minute.

#### MIN5_STATISTICS_QUEUE_EVERY (optional)

The frequency setting for 5 minute cron job. default `15s` (process per minute.)

#### MIN5_STATISTICS_BACKWARD_RANGE (optional)

Defined the range per processing backward (in minute). default 60 minutes.

#### MIN5_STATISTICS_NEXT_RANGE (optional)

Defined the range per processing forward (in minute). default 5 minute.

#### HOURLY_STATISTICS_QUEUE_EVERY (optional)

The frequency setting for 5 minute cron job. default `15s` (process per minute.)

#### HOURLY_STATISTICS_BACKWARD_RANGE (optional)

Defined the range per processing backward (in hour). default 24 hours.

#### HOURLY_STATISTICS_NEXT_RANGE (optional)

Defined the range per processing forward (in hour). default 1 hour.

#### DAILY_STATISTICS_QUEUE_EVERY (optional)

The frequency setting for cron job. default `15s` (process per minute.)

#### DAILY_STATISTICS_BACKWARD_RANGE (optional)

Defined the range per processing forward (in day). default 1 day.

#### DAILY_STATISTICS_NEXT_RANGE (optional)

Defined the range per processing forward (in day). default 1 day.
