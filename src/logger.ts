import winston from 'winston';
import dayjs from 'dayjs';
import DailyRotateFile from 'winston-daily-rotate-file';

const today = dayjs().startOf('date').format('YYYY-MM-DD');

const logger = winston.createLogger({
  level: process.env.DEBUG === 'true' ? 'debug' : 'info',
  format: winston.format.combine(winston.format.timestamp({ format: 'YYYY-MM-DDTHH:mm:ssZ' }), winston.format.json()),
  transports: [
    new DailyRotateFile({
      filename: `logs/${today}.%DATE%.error.log`,
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true, // 可選：壓縮歷史日誌文件
      maxSize: '20m', // 可選：每個日誌文件的最大大小
      maxFiles: '30d', // 可選：保留日誌文件的最長時間
      level: 'error', // 此設定僅適用於錯誤日誌文件
    }),
    new DailyRotateFile({
      filename: `logs/${today}.%DATE%.combined.log`,
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d',
    }),
    new winston.transports.Console({
      format: winston.format.simple(),
    }),
  ],
});

export default logger;
