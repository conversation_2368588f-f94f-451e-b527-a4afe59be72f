import { Job, DoneCallback } from 'bull';
import { Redis } from 'ioredis';
import { StatisticController } from '../controller/statisticCtrl';
import { STATISTIC_DIRECTION } from '../handlers/jobHandler';
import logger from '../logger';
import { Config, WorkerName } from '../config';
import { getProcessingAndProcessedTick, getStartAndStopTime, isInconsistentTimeTick, regxTaskName } from '../utils';
import { ReturnProcessTick } from '../repository/timeTickRepository';
import { TimePeriod } from '../repository/influxdbRepository';
import { processWalletHistory } from './walletHistoryWorker';
import { processTableWalletHistory } from './tableWalletHistoryWorker';
import { processSpinHistory } from './spinHistoryWorker';
import { processTableHistory } from './tableHistoryWorker';
import { processGameDistributionMul } from './gameDistributionWorker';
import { InfluxTableName } from '../db';

export type WorkerProps = {
  config: Config;
  statisticController: StatisticController;
  timeTick: ReturnProcessTick;
  taskName: string;
  direction: STATISTIC_DIRECTION;
  jobData: StatisticJobPayload;
  timePeriod: TimePeriod;
};

export type JackpotSummary = {
  partnerCode: string;
  gameID: string;
  playerID: string;
  betAmount: number;
  winAmount: number;
};

export type StoreToRedisJobPayload = {
  repeatDelay: number;
  storeEach?: boolean;
};

export type StatisticJobPayload = StoreToRedisJobPayload & {
  sourceMeasurement: InfluxTableName;
};

type ProcessStatisticWorkerPayload = {
  jobName: string;
  workerName: WorkerName;
  statisticController: StatisticController;
  redis: Redis;
  config: Config;
};

const MAX_RETRY_TIMES = 1;

const errorCountMap: Record<string, number> = {};

export const workerManager =
  ({
    jobName,
    workerName,
    statisticController,
    redis,
    config,
  }: {
    jobName: string;
    workerName: WorkerName;
    statisticController: StatisticController;
    redis: Redis;
    config: Config;
  }) =>
  async (job: Job<StatisticJobPayload>, done: DoneCallback) => {
    await processStatisticWorker(
      job,
      {
        jobName,
        workerName,
        statisticController,
        redis,
        config,
      },
      done,
    );
  };

const processStatisticWorker = async (
  job: Job<StatisticJobPayload>,
  { jobName, workerName, statisticController, config }: ProcessStatisticWorkerPayload,
  done: DoneCallback,
) => {
  const [, taskName, direction] = jobName.match(regxTaskName) ?? [];

  try {
    const timeTick = await statisticController.getProcessTimeTick(taskName, direction as STATISTIC_DIRECTION);

    logger.silly(
      `${
        process.pid
      } 👉 ~ [${taskName}-${direction}] ~ In workerManager ~ will do task in ${timeTick.processTimeTick?.toISOString()}`,
    );

    // Safety check: Initialize null ticks to prevent inconsistent state
    // Skip for GameDistributionWorker as it has its own initialization logic
    const needsInitialization =
      workerName !== WorkerName.GameDistributionWorker &&
      ((direction === 'next' && (!timeTick.processingTickNext || !timeTick.processedTickNext)) ||
        (direction === 'backward' && (!timeTick.processingTickBackward || !timeTick.processedTickBackward)));

    if (needsInitialization) {
      // Use system_start_tick as the starting point for initialization
      // This ensures we start from the appropriate time when workers restart
      const fallbackTick = timeTick.systemStartTick;
      logger.warn(
        `${
          process.pid
        } ⚠️ ~ [${taskName}-${direction}] ~ Initializing null time ticks with systemStartTick (${fallbackTick.toISOString()})`,
      );

      await statisticController.updateProcessingTimeTick(taskName, fallbackTick, direction as STATISTIC_DIRECTION);
      await statisticController.updateProcessedTimeTick(taskName, fallbackTick, direction as STATISTIC_DIRECTION);

      // Refresh timeTick after initialization
      const refreshedTimeTick = await statisticController.getProcessTimeTick(
        taskName,
        direction as STATISTIC_DIRECTION,
      );
      Object.assign(timeTick, refreshedTimeTick);
    }

    // TODO: if inconsistent time tick active, we should delete the data in range of inconsistent time tick,
    // than we should rollback the time tick to the last consistent time tick.
    if (isInconsistentTimeTick(timeTick, direction as STATISTIC_DIRECTION)) {
      const [processingTick, processedTick] = getProcessingAndProcessedTick(timeTick, direction as STATISTIC_DIRECTION);

      const [startTime, stopTime] = getStartAndStopTime(
        processingTick,
        processedTick,
        direction as STATISTIC_DIRECTION,
      );

      await statisticController.rollbackDataInRange(
        taskName,
        direction as STATISTIC_DIRECTION,
        timeTick,
        startTime,
        stopTime,
      );

      const skipMessage = `${
        process.pid
      } 🥵 ~ [${taskName}-${direction}] ~ In workerManager ~ inconsistent process time: (${processingTick?.toISOString()} !== ${processedTick?.toISOString()}), ROLLBACK`;

      logger.info(skipMessage);
      done(null, skipMessage);
      return;
    }

    let result = '';
    switch (workerName) {
      case WorkerName.WalletHistoryWorker:
        result = await processWalletHistory({
          config,
          statisticController,
          timeTick,
          taskName,
          direction: direction as STATISTIC_DIRECTION,
          jobData: job.data,
          timePeriod: '1m',
        });
        break;

      case WorkerName.TableWalletHistoryWorker:
        result = await processTableWalletHistory({
          config,
          statisticController,
          timeTick,
          taskName,
          direction: direction as STATISTIC_DIRECTION,
          jobData: job.data,
          timePeriod: '1m',
        });
        break;

      case WorkerName.SpinHistoryWorker:
        result = await processSpinHistory({
          config,
          statisticController,
          timeTick,
          taskName,
          direction: direction as STATISTIC_DIRECTION,
          jobData: job.data,
          timePeriod: '1m',
        });
        break;

      case WorkerName.TableHistoryWorker:
        result = await processTableHistory({
          config,
          statisticController,
          timeTick,
          taskName,
          direction: direction as STATISTIC_DIRECTION,
          jobData: job.data,
          timePeriod: '1m',
        });
        break;

      case WorkerName.GameDistributionWorker:
        result = await processGameDistributionMul({
          config,
          statisticController,
          timeTick,
          taskName,
          direction: direction as STATISTIC_DIRECTION,
          jobData: job.data,
          timePeriod: '1m',
        });
        break;

      default:
        logger.warn(
          `${process.pid} 🫠 ~ [${taskName}-${direction}] ~ worker:index.ts ~ workerManager ~ processTimeTick: ${timeTick.processTimeTick} do nothing in default task: ${taskName}`,
        );
        break;
    }

    done(null, result);
  } catch (error: unknown) {
    const errorContext = {
      jobName,
      taskName,
      direction,
      pid: process.pid,
      errorDetails: {
        message: (error as Error).message,
        name: (error as Error).name,
        code: (error as { code?: string }).code,
      },
    };

    const errorMsg = `${
      process.pid
    } ❌ ~ [${taskName}-${direction}] ~ worker:index.ts ~ workerManager ~ jobName: ${jobName} error occur: ${
      error as Error
    }.stack`;

    statisticController.sendKafkaMessage(errorMsg);
    logger.error('Worker error:', {
      error: error,
      stack: (error as Error).stack,
      context: errorContext,
    });

    done(error as Error);

    errorCountMap[(error as Error).message] = (errorCountMap[(error as Error).message] || 0) + 1;
    if (errorCountMap[(error as Error).message] >= MAX_RETRY_TIMES) {
      delete errorCountMap[(error as Error).message];
      process.exit(1);
    }
  }
};
