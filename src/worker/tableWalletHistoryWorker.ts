import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { WorkerProps } from '.';
import { STATISTIC_DIRECTION } from '../handlers/jobHandler';
import { ReturnProcessTick } from '../repository/timeTickRepository';
import logger from '../logger';
import { QueueConfig, getQueueConfig } from '../config';
import ms from 'ms';

dayjs.extend(utc);
dayjs.extend(timezone);

type InfluxTableWalletType = {
  _time: string;
  _measurement: string;
  action: string;
  partner_code: string;
  player_id: string;
  Amount: number;
  Balance: number;
  PartnerTransactionID: string;
  TransactionID: string;
};

function settleTimeRange(walletQueue: QueueConfig, timeTick: ReturnProcessTick, direction: STATISTIC_DIRECTION) {
  const backwardRange = walletQueue.statisticBackwardRange;
  const nextRange = walletQueue.statisticNextRange;

  // prevent null value cause toISOString error
  // Use systemStartTick as fallback for proper initialization
  const safeProcessTimeTick = timeTick.processTimeTick || timeTick.systemStartTick;

  const settleTimeHash = {
    [STATISTIC_DIRECTION.BACKWARD]: () => {
      const startTime = dayjs(safeProcessTimeTick).startOf('minute').subtract(backwardRange, 'minute').toDate();
      const stopTime = dayjs(safeProcessTimeTick).startOf('minute').toDate();
      const dir = STATISTIC_DIRECTION.BACKWARD;
      const recordTimeTick = startTime;
      return { stopTime, startTime, dir, recordTimeTick };
    },
    [STATISTIC_DIRECTION.NEXT]: () => {
      // change the `start time` and `stop time` for "next" direction.
      const now = dayjs().startOf('minute').toDate();
      const startTime = dayjs(safeProcessTimeTick).startOf('minute').toDate();

      // if the stopTime will be greater than now, then we should use now as stopTime
      // (only in minutely could be used in this way).
      const potentialTime = dayjs(startTime).add(nextRange, 'minute').toDate();
      const stopTime = potentialTime > now ? now : potentialTime;

      const dir = STATISTIC_DIRECTION.NEXT;
      const recordTimeTick = stopTime;
      return { stopTime, startTime, dir, recordTimeTick };
    },
  };

  return settleTimeHash[direction]();
}

export async function processTableWalletHistory({
  taskName,
  timeTick,
  direction,
  jobData,
  statisticController,
  timePeriod = '1m',
  config,
}: WorkerProps) {
  const walletQueue = getQueueConfig(taskName) as QueueConfig;

  const { startTime, stopTime, dir, recordTimeTick } = settleTimeRange(walletQueue, timeTick, direction);

  if (dayjs(startTime).startOf('minute') >= dayjs(stopTime).startOf('minute'))
    return `skip in ${taskName}-${direction} Invalid time range (${startTime.toISOString()} ~ ${stopTime.toISOString()}) no need to processed.`;

  if (stopTime < timeTick.firstRecordTick || stopTime >= dayjs().startOf('minute').toDate())
    return `skip in ${taskName}-${direction} (${startTime.toISOString()} ~ ${stopTime.toISOString()}) no need to processed.`;

  logger.info(
    `${
      process.pid
    } 👉 ~ [${taskName}-${direction}] ~ processTableWalletHistory ~ (${startTime.toISOString()} ~ ${stopTime.toISOString()}) (${dir} in ${timePeriod})`,
  );

  try {
    const countingStart = new Date();
    await statisticController.updateProcessingTimeTick(taskName, recordTimeTick, dir);

    logger.debug(
      `${
        process.pid
      } 👉 ~ [${taskName}-${direction}] ~ Start processing... (${startTime.toISOString()} ~ ${stopTime.toISOString()})`,
    );

    let rowLength = 0;
    let writeTimeTick = new Date();
    let clickhouseWriteTime = 0;
    await statisticController.getRawData<InfluxTableWalletType>({
      bucket: config.SOURCE_INFLUXDB_BUCKET,
      startTime: startTime.toISOString(),
      stopTime: stopTime.toISOString(),
      fromMeasurement: jobData.sourceMeasurement,
      callback: async (rows) => {
        const executeTime = new Date().getTime() - writeTimeTick.getTime();
        if (executeTime < ms('1s')) {
          await new Promise((resolve) => setTimeout(resolve, ms('1s') - executeTime));
        }
        writeTimeTick = new Date();

        rowLength += rows.length;

        const startInsertTime = new Date();
        await statisticController.insertTableWalletHistoryRecords(rows);
        const stopInsertTime = new Date();

        clickhouseWriteTime += stopInsertTime.getTime() - startInsertTime.getTime();

        const insertTime = stopInsertTime.getTime() - startInsertTime.getTime();

        const insertEnd = new Date();
        logger.debug(
          `${
            process.pid
          } 👉 ~ [${taskName}-${direction}] ~ insert table wallet history (${writeTimeTick.toISOString()} ~ ${insertEnd.toISOString()}) total time: ${insertTime} ms`,
        );
      },
    });

    logger.debug(
      `${
        process.pid
      } 👉 ~ [${taskName}-${direction}] ~ Finish fetchig from influx & upload result for processing (${startTime.toISOString()} ~ ${stopTime.toISOString()} (${dir} in ${timePeriod}))...`,
    );

    const countingInfluxEnd = new Date();
    await statisticController.updateProcessedTimeTick(taskName, recordTimeTick, dir);
    const influxReadTime = countingInfluxEnd.getTime() - countingStart.getTime();
    logger.info(
      `${
        process.pid
      } 👉 ~ [${taskName}-${direction}] ~ Finish Processed (${startTime.toISOString()} ~ ${stopTime.toISOString()}) [total: ${rowLength}] (COST: InfluxRead=${influxReadTime}ms ; ClickhouseWrite=${clickhouseWriteTime}ms) 💪`,
    );

    // notify if no data in the time range.
    if (config.HEALTHY_CHECK_NOTIFY && rowLength === 0) {
      const healthyCheckMsg =
        `❗️ No data in ${taskName}-${direction} ❗️\n(${startTime.toISOString()} ~ ${stopTime.toISOString()}).` +
        `\nPlease check the data source.`;
      logger.info(healthyCheckMsg);
      statisticController.sendKafkaMessage(healthyCheckMsg);
    }

    return `~ [${taskName}-${direction}] ~ success in ${timePeriod} (${startTime.toISOString()} ~ ${stopTime.toISOString()}) [total: ${rowLength}] (COST: InfluxRead=${influxReadTime}ms ; ClickhouseWrite=${clickhouseWriteTime}ms)`;
  } catch (error) {
    logger.error(
      `[${taskName}-${direction}] (${startTime.toISOString()} ~ ${stopTime.toISOString()}) ~ error: ${error}`,
    );

    await statisticController.rollbackDataInRange(taskName, direction, timeTick, startTime, stopTime);

    throw error;
  }
}
