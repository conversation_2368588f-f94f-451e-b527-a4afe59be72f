import { WorkerProps } from '.';
import dayjs from 'dayjs';
import { STATISTIC_DIRECTION } from '../handlers';
import logger from '../logger';
import { GameDistributionMulRecord } from '../model/spin';

export async function processGameDistributionMul({ statisticController, taskName, timeTick, direction }: WorkerProps) {
  if (direction != STATISTIC_DIRECTION.NEXT) {
    return '';
  }

  /**
   * Note: The processedTime/ProcessingTime is recorded as StartTime of the day, other than other workers.
   */
  let processedTime = timeTick.processedTickNext;
  if (processedTime === null) {
    processedTime = dayjs(timeTick.firstRecordTick).subtract(1, 'day').toDate();
    try {
      await statisticController.updateProcessingTimeTick(taskName, processedTime, STATISTIC_DIRECTION.NEXT);
      await statisticController.updateProcessedTimeTick(taskName, processedTime, STATISTIC_DIRECTION.NEXT);
    } catch (error) {
      logger.error(`[${taskName}] init time tick failed: ${error}`);
      throw error;
    }
  }

  const processingTime = dayjs(processedTime).add(1, 'day').toDate();

  if (!dayjs().startOf('day').add(1, 'hour').isAfter(dayjs(processingTime).add(1, 'day'))) {
    return 'skip at current time';
  }

  try {
    await statisticController.updateProcessingTimeTick(taskName, processingTime, STATISTIC_DIRECTION.NEXT);

    const res = await statisticController.clickhouseService.queryGameDistributionMul(processingTime);
    const { data } = await res.json();
    if (data.length !== 0) {
      const records = data.map((record) => ({
        ...(record as GameDistributionMulRecord),
        time: processingTime,
      }));
      await statisticController.clickhouseService.insertGameDistributionMul(records as GameDistributionMulRecord[]);
    }

    await statisticController.updateProcessedTimeTick(taskName, processingTime, STATISTIC_DIRECTION.NEXT);
  } catch (error) {
    logger.error(`[${taskName}] (${processingTime.toISOString()}) ~ error: ${error}`);
    throw error;
  }

  return '';
}
