import ms from 'ms';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import logger from '../logger';
import { WorkerProps } from '.';
import { STATISTIC_DIRECTION } from '../handlers/jobHandler';
import { ReturnProcessTick } from '../repository/timeTickRepository';
import { QueueConfig, getQueueConfig } from '../config';
import { NotificationConfig, NotificationName } from '../service/notificationService';
import { StatisticController } from '../controller/statisticCtrl';
import { LRUCache } from 'lru-cache';
dayjs.extend(utc);
dayjs.extend(timezone);

export type InfluxdbSpinRecordType = {
  _start: string;
  _stop: string;
  _measurement: string;
  game_id: string;
  partner_code: string;
  player_id: string;
  round_type: string;
  BalanceAfter: number;
  _time: string;
  BalanceBefore: number;
  BetAmount: number;
  IsFreeRounds: boolean;
  IsFreeSpins: boolean;
  RoundID: string;
  SpinID: string;
  State: string;
  WinAmount: number;
};

export type SpinState = {
  GameType: number;
  GameStage: number;
  BaseReelResultsScreen: string[][];
  FreeReelResultsScreen: string[][];
  RemainFreeSpins: number;
  AwardFreeSpins: number;
  BetInfo: {
    BaseCoins: number;
    Coins: number;
    CoinValue: number;
    TotalBetAmount: number;
  };
};

export type BigWinCollector = {
  partner_code: string;
  player_id: string;
  game_id: string;
  round_id: string;
  round_type: string;
  win_amount: number;
  bet_amount: number;
  time: string;
  isNotified: boolean;
  spin_ids?: string[];
};

const insertedSpins = new LRUCache<string, Date>({
  max: 100000, // max 100,000 records
  ttl: ms('10m'), // ttl 10 minutes
});
const bigWinCollector = new LRUCache<string, BigWinCollector>({
  max: 10000000, // max 10,000,000 records
  ttl: ms('1h'), // ttl 1 hour
});

function settleTimeRange(spinRecordQueue: QueueConfig, timeTick: ReturnProcessTick, direction: STATISTIC_DIRECTION) {
  const backwardRange = spinRecordQueue.statisticBackwardRange;
  const nextRange = spinRecordQueue.statisticNextRange;

  // prevent null value cause toISOString error
  // Use systemStartTick as fallback for proper initialization
  const safeProcessTimeTick = timeTick.processTimeTick || timeTick.systemStartTick;

  const settleTimeHash = {
    [STATISTIC_DIRECTION.BACKWARD]: () => {
      const startTime = dayjs(safeProcessTimeTick).startOf('minute').subtract(backwardRange, 'minute').toDate();
      const stopTime = dayjs(safeProcessTimeTick).startOf('minute').toDate();
      const dir = STATISTIC_DIRECTION.BACKWARD;
      const recordTimeTick = startTime;
      return { stopTime, startTime, dir, recordTimeTick };
    },
    [STATISTIC_DIRECTION.NEXT]: () => {
      // change the `start time` and `stop time` for "next" direction.
      const now = dayjs().startOf('minute').toDate();
      const startTime = dayjs(safeProcessTimeTick).startOf('minute').toDate();

      // if the stopTime will be greater than now, then we should use now as stopTime
      // (only in minutely could be used in this way).
      const potentialTime = dayjs(startTime).add(nextRange, 'minute').toDate();
      const stopTime = potentialTime > now ? now : potentialTime;

      const dir = STATISTIC_DIRECTION.NEXT;
      const recordTimeTick = stopTime;
      return { stopTime, startTime, dir, recordTimeTick };
    },
  };

  return settleTimeHash[direction]();
}

function collectBigWinToNotify({
  spinRecords,
  notificationConfig,
  statisticController,
}: {
  spinRecords: InfluxdbSpinRecordType[];
  notificationConfig: NotificationConfig;
  statisticController: StatisticController;
}) {
  for (const record of spinRecords) {
    const { RoundID, SpinID, WinAmount, BetAmount, partner_code, player_id, game_id, round_type, _time, State } =
      record;
    const state = JSON.parse(State) as SpinState;

    const key = `${partner_code}-${player_id}-${RoundID}`;

    // calculate the total win and total bet, and multiple
    let bigWinCollectorRecord = bigWinCollector.get(key);
    if (!bigWinCollectorRecord) {
      bigWinCollector.set(key, {
        round_id: RoundID,
        partner_code,
        player_id,
        game_id,
        round_type,
        win_amount: WinAmount,
        bet_amount: BetAmount,
        time: _time,
        isNotified: false,
        spin_ids: [SpinID],
      });
      bigWinCollectorRecord = bigWinCollector.get(key)!;
    } else {
      bigWinCollectorRecord.win_amount += WinAmount;
      bigWinCollectorRecord.bet_amount += BetAmount;
      bigWinCollectorRecord.round_type = round_type;
      bigWinCollectorRecord.spin_ids?.push(SpinID);
    }

    // if should send notification, then send notification
    if (
      !bigWinCollectorRecord.isNotified &&
      bigWinCollectorRecord.bet_amount > 0 &&
      statisticController.shouldSendNotification(bigWinCollectorRecord, notificationConfig) &&
      state.RemainFreeSpins === 0
    ) {
      statisticController.sendNotification(bigWinCollectorRecord);
      bigWinCollectorRecord.isNotified = true;
    }

    if (state.RemainFreeSpins === 0) {
      bigWinCollector.delete(key);
    }
  }

  bigWinCollector.forEach((value, key) => {
    logger.debug(
      `${process.pid} 👉 ~ collectBigWinToNotify: [bigWinCollector] (${
        bigWinCollector.size
      }): ~ ${key} - ${JSON.stringify(value)}`,
    );
  });
}

export async function processSpinHistory({
  taskName,
  timeTick,
  direction,
  jobData,
  statisticController,
  timePeriod = '1m',
  config,
}: WorkerProps) {
  const spinRecordQueue = getQueueConfig(taskName) as QueueConfig;

  // default setting the `start time` and `stop time` for "next" direction.
  const { startTime, stopTime, dir, recordTimeTick } = settleTimeRange(spinRecordQueue, timeTick, direction);

  if (dayjs(startTime).startOf('minute') >= dayjs(stopTime).startOf('minute'))
    return `skip in ${taskName}-${direction} Invalid time range (${startTime.toISOString()} ~ ${stopTime.toISOString()}) no need to processed.`;

  if (stopTime < timeTick.firstRecordTick || stopTime >= dayjs().startOf('minute').toDate())
    return `skip in ${taskName}-${direction} (${startTime.toISOString()} ~ ${stopTime.toISOString()}) no need to processed.`;

  logger.info(
    `${
      process.pid
    } 👉 ~ [${taskName}-${direction}] ~ processSpinHistory ~ (${startTime.toISOString()} ~ ${stopTime.toISOString()}) (${dir} in ${timePeriod})`,
  );

  // write `stop time` to processing time tick.
  try {
    const countingStart = new Date();
    let countingInfluxEnd = new Date();
    await statisticController.updateProcessingTimeTick(taskName, recordTimeTick, dir);

    // collect big win to notify
    const notificationConfig = await statisticController.getNotificationConfig(NotificationName.Notify);

    logger.debug(
      `${
        process.pid
      } 👉 ~ [${taskName}-${direction}] ~ Start processing... (${startTime.toISOString()} ~ ${stopTime.toISOString()})`,
    );

    let rowLength = 0;
    let clickhouseWriteTime = 0;
    let writeTimeTick = new Date();
    let spinIDs: string[] = [];
    await statisticController.getRawData<InfluxdbSpinRecordType>({
      bucket: config.SOURCE_INFLUXDB_BUCKET,
      startTime: startTime.toISOString(),
      stopTime: stopTime.toISOString(),
      fromMeasurement: jobData.sourceMeasurement,

      // by using callback function to insert the data to clickhouse
      // to avoid out of memory issue.
      async callback(rows) {
        const executeTime = new Date().getTime() - writeTimeTick.getTime();
        if (executeTime < ms('1s')) {
          await new Promise((resolve) => setTimeout(resolve, ms('1s') - executeTime));
        }
        writeTimeTick = new Date();

        // filter out the rows without SpinID
        rows = rows.filter((row) => row.SpinID);

        // filter out SpinID already inserted
        rows = rows.filter((row) => !insertedSpins.has(row.SpinID));
        spinIDs = rows.map((row) => row.SpinID);

        rowLength += rows.length;

        const startInsertTime = new Date();
        await statisticController.insertSpinRecords(rows);
        const stopInsertTime = new Date();

        // collect big win to notify
        collectBigWinToNotify({
          spinRecords: rows,
          notificationConfig,
          statisticController,
        });

        clickhouseWriteTime += stopInsertTime.getTime() - startInsertTime.getTime();
      },
    });

    countingInfluxEnd = new Date();

    logger.debug(
      `${
        process.pid
      } 👉 ~ [${taskName}-${direction}] ~ Finish sumUp & upload result for processing (${startTime.toISOString()} ~ ${stopTime.toISOString()} (${dir} in ${timePeriod}))...`,
    );

    // write `stop time` to processed time tick.
    await statisticController.updateProcessedTimeTick(taskName, recordTimeTick, dir);

    const influxReadTime = countingInfluxEnd.getTime() - countingStart.getTime();
    logger.info(
      `${
        process.pid
      } 👉 ~ [${taskName}-${direction}] ~ Finish Processed (${startTime.toISOString()} ~ ${stopTime.toISOString()}) [total: ${rowLength}] (COST: InfluxRead=${influxReadTime}ms ; ClickhouseWrite=${clickhouseWriteTime}ms) 💪`,
    );

    // save inserted spins
    const currentTime = new Date();
    spinIDs.forEach((spinID) => insertedSpins.set(spinID, currentTime));

    // notify if no data in the time range.
    if (config.HEALTHY_CHECK_NOTIFY && rowLength === 0) {
      const healthyCheckMsg =
        `❗️ No data in ${taskName}-${direction} ❗️\n(${startTime.toISOString()} ~ ${stopTime.toISOString()}).` +
        `\nPlease check the data source.`;
      logger.info(healthyCheckMsg);
      statisticController.sendKafkaMessage(healthyCheckMsg);
    }

    return `~ [${taskName}-${direction}] ~ success in ${timePeriod} (${startTime.toISOString()} ~ ${stopTime.toISOString()}) [total: ${rowLength}] (COST: InfluxRead=${influxReadTime}ms ; ClickhouseWrite=${clickhouseWriteTime}ms)`;
  } catch (error) {
    logger.error(
      `[${taskName}-${direction}] (${startTime.toISOString()} ~ ${stopTime.toISOString()}) ~ error: ${error}`,
    );

    // rollback the time tick
    await statisticController.rollbackDataInRange(taskName, direction, timeTick, startTime, stopTime);

    throw error;
  }
}
