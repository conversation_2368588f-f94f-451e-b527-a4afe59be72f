import ms from 'ms';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import logger from '../logger';
import { WorkerProps } from '.';
import { STATISTIC_DIRECTION } from '../handlers/jobHandler';
import { ReturnProcessTick } from '../repository/timeTickRepository';
import { QueueConfig, getQueueConfig } from '../config';
import { NotificationConfig, NotificationName } from '../service/notificationService';
import { StatisticController } from '../controller/statisticCtrl';
import { LRUCache } from 'lru-cache';

dayjs.extend(utc);
dayjs.extend(timezone);

export type InfluxdbTableRecordType = {
  _start: string;
  _stop: string;
  _measurement: string;
  game_id: string;
  partner_code: string;
  player_id: string;
  round_type: string;
  BalanceAfter: number;
  _time: string;
  BalanceBefore: number;
  BetAmount: number;
  RoundID: string;
  EventID: string;
  State: string;
  WinAmount: number;
};

export type TableState = {
  EventType: number;
  GameStage: number;
  ExtendedState: {
    ActionID: string;
    AutoCashOut: number;
    AutoCashOutHalf: number;
    CoinValue: number;
    RoundID: string;
    WalletIntegrated: string;
    gameCashOut: boolean;
    gameCashOutHalf: boolean;
    gameCashOutMultiply: number;
    gameCashOutMultiplyHalf: number;
    roundIdx: number;
    hash: string;
  };
};

export type BigWinCollector = {
  partner_code: string;
  player_id: string;
  game_id: string;
  round_id: string;
  round_type: string;
  win_amount: number;
  bet_amount: number;
  time: string;
  isNotified: boolean;
  event_ids?: string[];
};

const insertedTables = new LRUCache<string, Date>({
  max: 100000, // max 100,000 records
  ttl: ms('10m'), // ttl 10 minutes
});
const bigWinCollector = new LRUCache<string, BigWinCollector>({
  max: 10000000, // max 10,000,000 records
  ttl: ms('1h'), // ttl 1 hour
});

function settleTimeRange(tableRecordQueue: QueueConfig, timeTick: ReturnProcessTick, direction: STATISTIC_DIRECTION) {
  const backwardRange = tableRecordQueue.statisticBackwardRange;
  const nextRange = tableRecordQueue.statisticNextRange;

  // prevent null value cause toISOString error
  // Use systemStartTick as fallback for proper initialization
  const safeProcessTimeTick = timeTick.processTimeTick || timeTick.systemStartTick;

  const settleTimeHash = {
    [STATISTIC_DIRECTION.BACKWARD]: () => {
      const startTime = dayjs(safeProcessTimeTick).startOf('minute').subtract(backwardRange, 'minute').toDate();
      const stopTime = dayjs(safeProcessTimeTick).startOf('minute').toDate();
      const dir = STATISTIC_DIRECTION.BACKWARD;
      const recordTimeTick = startTime;
      return { stopTime, startTime, dir, recordTimeTick };
    },
    [STATISTIC_DIRECTION.NEXT]: () => {
      // change the `start time` and `stop time` for "next" direction.
      const now = dayjs().startOf('minute').toDate();
      const startTime = dayjs(safeProcessTimeTick).startOf('minute').toDate();

      // if the stopTime will be greater than now, then we should use now as stopTime
      // (only in minutely could be used in this way).
      const potentialTime = dayjs(startTime).add(nextRange, 'minute').toDate();
      const stopTime = potentialTime > now ? now : potentialTime;

      const dir = STATISTIC_DIRECTION.NEXT;
      const recordTimeTick = stopTime;
      return { stopTime, startTime, dir, recordTimeTick };
    },
  };

  return settleTimeHash[direction]();
}

function collectBigWinToNotify({
  tableRecords,
  notificationConfig,
  statisticController,
}: {
  tableRecords: InfluxdbTableRecordType[];
  notificationConfig: NotificationConfig;
  statisticController: StatisticController;
}) {
  for (const record of tableRecords) {
    const { RoundID, EventID, WinAmount, BetAmount, partner_code, player_id, game_id, round_type, _time, State } =
      record;
    const state = JSON.parse(State) as TableState;

    const key = `${partner_code}-${player_id}-${RoundID}`;

    // calculate the total win and total bet, and multiple
    let bigWinCollectorRecord = bigWinCollector.get(key);
    if (!bigWinCollectorRecord) {
      bigWinCollector.set(key, {
        round_id: RoundID,
        partner_code,
        player_id,
        game_id,
        round_type,
        win_amount: WinAmount,
        bet_amount: BetAmount,
        time: _time,
        isNotified: false,
        event_ids: [EventID],
      });
      bigWinCollectorRecord = bigWinCollector.get(key)!;
    } else {
      bigWinCollectorRecord.win_amount += WinAmount;
      bigWinCollectorRecord.bet_amount += BetAmount;
      bigWinCollectorRecord.round_type = round_type;
      bigWinCollectorRecord.event_ids?.push(EventID);
    }

    // if should send notification, then send notification
    if (
      !bigWinCollectorRecord.isNotified &&
      statisticController.shouldSendNotification(bigWinCollectorRecord, notificationConfig) &&
      state.ExtendedState.AutoCashOut === 0
    ) {
      statisticController.sendNotification(bigWinCollectorRecord);
      bigWinCollectorRecord.isNotified = true;
    }

    if (state.ExtendedState.AutoCashOut === 0) {
      bigWinCollector.delete(key);
    }
  }

  bigWinCollector.forEach((value, key) => {
    logger.debug(
      `${process.pid} 👉 ~ collectBigWinToNotify: [bigWinCollector] (${
        bigWinCollector.size
      }): ~ ${key} - ${JSON.stringify(value)}`,
    );
  });
}

export async function processTableHistory({
  taskName,
  timeTick,
  direction,
  jobData,
  statisticController,
  timePeriod = '1m',
  config,
}: WorkerProps) {
  const tableRecordQueue = getQueueConfig(taskName) as QueueConfig;

  // default setting the `start time` and `stop time` for "next" direction.
  const { startTime, stopTime, dir, recordTimeTick } = settleTimeRange(tableRecordQueue, timeTick, direction);

  if (dayjs(startTime).startOf('minute') >= dayjs(stopTime).startOf('minute'))
    return `skip in ${taskName}-${direction} Invalid time range (${startTime.toISOString()} ~ ${stopTime.toISOString()}) no need to processed.`;

  if (stopTime < timeTick.firstRecordTick || stopTime >= dayjs().startOf('minute').toDate())
    return `skip in ${taskName}-${direction} (${startTime.toISOString()} ~ ${stopTime.toISOString()}) no need to processed.`;

  logger.info(
    `${
      process.pid
    } 👉 ~ [${taskName}-${direction}] ~ processTableHistory ~ (${startTime.toISOString()} ~ ${stopTime.toISOString()}) (${dir} in ${timePeriod})`,
  );

  // write `stop time` to processing time tick.
  try {
    const countingStart = new Date();
    let countingInfluxEnd = new Date();
    await statisticController.updateProcessingTimeTick(taskName, recordTimeTick, dir);

    // collect big win to notify
    const notificationConfig = await statisticController.getNotificationConfig(NotificationName.Notify);

    logger.debug(
      `${
        process.pid
      } 👉 ~ [${taskName}-${direction}] ~ Start processing... (${startTime.toISOString()} ~ ${stopTime.toISOString()})`,
    );

    let rowLength = 0;
    let clickhouseWriteTime = 0;
    let writeTimeTick = new Date();
    let eventIDs: string[] = [];
    await statisticController.getTableGameRawData<InfluxdbTableRecordType>({
      bucket: config.SOURCE_INFLUXDB_TABLE_BUCKET,
      startTime: startTime.toISOString(),
      stopTime: stopTime.toISOString(),
      fromMeasurement: jobData.sourceMeasurement,

      // by using callback function to insert the data to clickhouse
      // to avoid out of memory issue.
      async callback(rows) {
        const executeTime = new Date().getTime() - writeTimeTick.getTime();
        if (executeTime < ms('1s')) {
          await new Promise((resolve) => setTimeout(resolve, ms('1s') - executeTime));
        }
        writeTimeTick = new Date();

        // filter out the rows without EventID
        rows = rows.filter((row) => row.EventID);

        // filter out EventID already inserted
        rows = rows.filter((row) => !insertedTables.has(row.EventID));
        eventIDs = rows.map((row) => row.EventID);

        rowLength += rows.length;

        const startInsertTime = new Date();
        await statisticController.insertTableGameRecords(rows);
        const stopInsertTime = new Date();

        // collect big win to notify
        collectBigWinToNotify({
          tableRecords: rows,
          notificationConfig,
          statisticController,
        });

        clickhouseWriteTime += stopInsertTime.getTime() - startInsertTime.getTime();
      },
    });

    countingInfluxEnd = new Date();

    logger.debug(
      `${
        process.pid
      } 👉 ~ [${taskName}-${direction}] ~ Finish sumUp & upload result for processing (${startTime.toISOString()} ~ ${stopTime.toISOString()} (${dir} in ${timePeriod}))...`,
    );

    // write `stop time` to processed time tick.
    await statisticController.updateProcessedTimeTick(taskName, recordTimeTick, dir);

    const influxReadTime = countingInfluxEnd.getTime() - countingStart.getTime();
    logger.info(
      `${
        process.pid
      } 👉 ~ [${taskName}-${direction}] ~ Finish Processed (${startTime.toISOString()} ~ ${stopTime.toISOString()}) [total: ${rowLength}] (COST: InfluxRead=${influxReadTime}ms ; ClickhouseWrite=${clickhouseWriteTime}ms) 💪`,
    );

    // save inserted spins
    const currentTime = new Date();
    eventIDs.forEach((eventID) => insertedTables.set(eventID, currentTime));

    // notify if no data in the time range.
    if (config.HEALTHY_CHECK_NOTIFY && rowLength === 0) {
      const healthyCheckMsg =
        `❗️ No data in ${taskName}-${direction} ❗️\n(${startTime.toISOString()} ~ ${stopTime.toISOString()}).` +
        `\nPlease check the data source.`;
      logger.info(healthyCheckMsg);
      statisticController.sendKafkaMessage(healthyCheckMsg);
    }

    return `~ [${taskName}-${direction}] ~ success in ${timePeriod} (${startTime.toISOString()} ~ ${stopTime.toISOString()}) [total: ${rowLength}] (COST: InfluxRead=${influxReadTime}ms ; ClickhouseWrite=${clickhouseWriteTime}ms)`;
  } catch (error) {
    logger.error(
      `[${taskName}-${direction}] (${startTime.toISOString()} ~ ${stopTime.toISOString()}) ~ error: ${error}`,
    );

    // rollback the time tick
    await statisticController.rollbackDataInRange(taskName, direction, timeTick, startTime, stopTime);

    throw error;
  }
}
