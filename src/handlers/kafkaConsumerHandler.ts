import { Consumer } from 'kafkajs';
import { StatisticController } from '../controller/statisticCtrl';
import { Config } from '../config';
import { SumUpResult } from '../repository/influxdbRepository';
import { Granularity } from '../repository/totalSumUpRepository';
import logger from '../logger';

export type TotalSumUpPayload = {
  influxdbSumUpResult: SumUpResult[];
  granularity: Granularity;
};

export class KafkaConsumerHandler {
  private statisticController: StatisticController;
  private kafkaConsumer: Consumer;
  private config: Config;
  constructor({
    statisticController,
    config,
    kafkaConsumer,
  }: {
    statisticController: StatisticController;
    config: Config;
    kafkaConsumer: Consumer;
  }) {
    this.config = config;
    this.statisticController = statisticController;
    this.kafkaConsumer = kafkaConsumer;
  }

  async setup() {
    await this.kafkaConsumer.connect();
    await this.kafkaConsumer.subscribe({ topic: this.config.KAFKA_TOPIC_TOTAL_SUM_UP, fromBeginning: true });
    await this.kafkaConsumer.run({
      eachMessage: async ({ topic, message }) => {
        switch (topic) {
          case this.config.KAFKA_TOPIC_TOTAL_SUM_UP: {
            if (!message.value) return;
            const payload = JSON.parse(message.value.toString()) as TotalSumUpPayload;
            if (!payload.influxdbSumUpResult || !payload.granularity) return;

            await this.handleTotalSumUpMessage(payload);
            break;
          }
          default:
            break;
        }
      },
    });
  }

  private async handleTotalSumUpMessage(payload: TotalSumUpPayload) {
    const { influxdbSumUpResult, granularity } = payload;
    try {
      await this.statisticController.upSertTotalSumUp(influxdbSumUpResult, granularity);
    } catch (error) {
      const errorMsg = `❌ handleTotalSumUpMessage ~ error: ${error}`;
      console.error(errorMsg);
      logger.error(errorMsg);
    }
  }
}
