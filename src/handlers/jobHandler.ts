import Bull, { EveryRepeatOptions, Queue } from 'bull';
import { Cron, QueueConfig, getRepeatCron } from '../config';
import { StatisticController, TimeTickPayloadHash } from '../controller/statisticCtrl';
import logger from '../logger';
import { StatisticJobPayload } from '../worker';
import ms from 'ms';

export interface QueueHash {
  [name: string]: Queue;
}

export enum STATISTIC_DIRECTION {
  BACKWARD = 'backward',
  NEXT = 'next',
}
export class JobHandler {
  private queues: Queue[] = [];
  private queueHash: QueueHash = {};
  private statisticController: StatisticController;
  private redisUrl: string;
  private timeTickPayloadHash: TimeTickPayloadHash;
  private confQueues: QueueConfig[];

  constructor({
    statisticController,
    confQueues,
    redisUrl,
    timeTickPayloadHash,
  }: {
    statisticController: StatisticController;
    confQueues: QueueConfig[];
    redisUrl: string;
    timeTickPayloadHash: TimeTickPayloadHash;
  }) {
    this.statisticController = statisticController;
    this.redisUrl = redisUrl;
    this.timeTickPayloadHash = timeTickPayloadHash;
    this.queues = [];
    this.confQueues = confQueues;

    for (let i = 0; i < confQueues.length; i++) {
      const qc = confQueues[i];

      const res = this.spawnWorkingQueue(qc, this.queues, this.queueHash, STATISTIC_DIRECTION.NEXT);
      this.queues = res.queues;
      this.queueHash = res.queueHash;

      // if process tick backward is greater than first record tick, then we need to process backward.
      if (qc.shouldSpawnBackward && this.statisticController.shouldProcessBackward(this.timeTickPayloadHash[qc.name])) {
        const res = this.spawnWorkingQueue(qc, this.queues, this.queueHash, STATISTIC_DIRECTION.BACKWARD);
        this.queues = res.queues;
        this.queueHash = res.queueHash;
      }
    }
  }

  start() {
    for (let i = 0; i < this.queues.length; i++) {
      const queue = this.queues[i];

      // Completed Jobs listener
      queue.on('global:completed', this.handleJobCompleted(queue).bind(this));
      // Failed Jobs listener
      queue.on('global:failed', this.handleJobFailed(queue).bind(this));
    }

    return { queues: this.queues, queueHash: this.queueHash };
  }

  shouldProcessBackward(queueConfigName: string) {
    const cq = this.confQueues.find((qc) => qc.name === queueConfigName);

    if (!cq) throw new Error(`❌ shouldProcessBackward ~ qc not found for ${queueConfigName}`);

    return this.statisticController.shouldProcessBackward(this.timeTickPayloadHash[queueConfigName]);
  }

  getQueues() {
    return this.queues;
  }

  getQueue(name: string) {
    return this.queueHash[name];
  }

  private spawnWorkingQueue(
    qc: QueueConfig,
    queues: Queue[] = [],
    queueHash: QueueHash,
    postfix?: STATISTIC_DIRECTION,
  ) {
    if (!postfix) {
      logger.error(`❌ no postfix found for ${qc.name}!`);
      throw new Error(`no postfix found for ${qc.name}!`);
    }

    const qName = `${qc.name}-${postfix}`;

    const queue = new Bull(qName, this.redisUrl);

    const repeatDelay = qc.repeat[postfix] as EveryRepeatOptions;
    const jobDataPayload: StatisticJobPayload = {
      sourceMeasurement: qc.sourceInfluxdbMeasurement,
      repeatDelay: repeatDelay.every,
    };

    this.spawnQueue(qName, jobDataPayload, getRepeatCron(qc.name, postfix));
    return { queues: [...queues, queue], queueHash: { ...queueHash, [qName]: queue } };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private spawnQueue(qUName: string, jobData: object, _repeat?: Cron) {
    const queue = new Bull(qUName, this.redisUrl);

    queue.pause().then(() =>
      queue.obliterate({ force: true }).then(() => {
        queue.add(qUName, jobData);
      }),
    );

    return queue;
  }

  private handleJobCompleted(queue: Queue) {
    return async (jobId: string, _result: unknown) => {
      const job = await queue.getJob(jobId);
      logger.info(
        `${process.pid} 👉 ~ handleJobCompleted ~ return ~ job:${job?.id} - ${
          queue.name
        } -  【${_result}】[jobData:${JSON.stringify(job?.data)}]`,
      );

      // if preStoreWorker, remove StoreEach from job data to avoid store all statistics each time.
      if (job?.data && 'storeEach' in job.data) delete job?.data.storeEach;

      queue.add(queue.name, job?.data, {
        delay: job?.data.repeatDelay || ms('15s'),
      });
    };
  }

  private handleJobFailed(queue: Queue) {
    return async (jobId: string, _result: unknown) => {
      const job = await queue.getJob(jobId);
      logger.error(
        `${process.pid} ❌ ~ handleJobFailed ~ return ~ job: ${job?.id} 【${_result}】 [jobData:${JSON.stringify(
          job?.data,
        )}]`,
      );
      console.error('🚀 ~ JobHandler<T, ~ return ~ queue.name, job?.data:', queue.name, job?.data);

      queue.add(queue.name, job?.data, { delay: job?.data.repeatDelay });
    };
  }
}
