import { STATISTIC_DIRECTION } from '../handlers/jobHandler';
import { CountUpPayload, RawDataPayload, SumUpPayload, SumUpResult } from '../repository/influxdbRepository';
import { ReturnProcessTick, TimeTickPayload } from '../repository/timeTickRepository';
import { InfluxdbService } from '../service/influxdbService';
import { TimeTickService } from '../service/timeTickService';
import logger from '../logger';
import { Config, QUEUE_NAME } from '../config';
import { Producer } from 'kafkajs';
import { TotalSumUpService } from '../service/totalSumUpService';
import {
  AggregateSumUpPayload,
  Granularity,
  GranularitySumUp,
  GranularitySumUpType,
} from '../repository/totalSumUpRepository';
import { PreStoreService } from '../service/preStoreService';
import { Knex } from 'knex';
import { WalletDataService } from '../service/walletDataService';
import { WalletHistoryService } from '../service/walletHistoryService';
import { InfluxWalletType } from '../worker/walletHistoryWorker';
import { ClickhouseTableName } from '../db';
import { ClickhouseService } from '../service/clickhouseService';
import { BigWinCollector, InfluxdbSpinRecordType } from '../worker/spinHistoryWorker';
import { NotificationConfig, NotificationName, NotificationService } from '../service/notificationService';
import { GameService } from '../service/gameService';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { InfluxdbTableRecordType } from '../worker/tableHistoryWorker';

dayjs.extend(utc);
dayjs.extend(timezone);

export interface TimeTickPayloadHash {
  [key: string]: TimeTickPayload;
}
export class StatisticController {
  constructor(
    private config: Config,
    private kafkaProducer: Producer,
    private timeTickService: TimeTickService,
    private gameService: GameService,
    private influxdbService: InfluxdbService,
    public clickhouseService: ClickhouseService,
    private totalSumUpService: TotalSumUpService,
    private walletDataService: WalletDataService,
    private preStoreService: PreStoreService,
    private walletHistoryService: WalletHistoryService,
    public notificationService: NotificationService,
  ) {}

  async setup({ queueNames }: { queueNames: string[] }) {
    try {
      logger.info(`${process.pid} 👉 check if time_tick table exist ...`);
      // create table if not exist
      const isNewCreated = await this.timeTickService.checkTableIsNewCreated();
      logger.info(`${process.pid} 👉 in StatisticController: ~ createTableIfNotExist ~ isNewCreated: ${isNewCreated}`);

      if (isNewCreated) {
        // get first time tick
        const firstTimeTick = this.config.MANUAL_FIRST_TIME_TICK || (await this.influxdbService.getFirstTimeTick());
        logger.info(`${process.pid} 👉 setup ~ firstTimeTick: ${firstTimeTick}`);

        // insert first time tick
        await this.timeTickService.insertFirstTimeTick(firstTimeTick);
      }

      // find first time tick
      const firstTimeTick = await this.timeTickService.findOneByName(QUEUE_NAME.FIRST_TIME_TICK);

      // create queues
      const timeTicks = await Promise.all(
        queueNames.map(async (name) => {
          // Check if the time tick record already exists
          const existingTimeTick = await this.timeTickService.findOneByName(name, { fromMaster: true });

          if (!existingTimeTick) {
            // Only initialize if the record doesn't exist
            const initialTick = this.timeTickService.processToSuitTimeTick(name, new Date());
            await this.timeTickService.insertTimeTick({
              name,
              first_record_tick: firstTimeTick.first_record_tick,
              system_start_tick: initialTick,
              // Don't initialize processing/processed ticks - let them be null initially
              // They will be properly initialized by the worker logic when needed
            });
          }
          return await this.timeTickService.findOneByName(name, { fromMaster: true });
        }),
      );

      return timeTicks.reduce(
        (acc, timeTick) => ({
          ...acc,
          [timeTick.name]: timeTick,
        }),
        {} as TimeTickPayloadHash,
      );
    } catch (error) {
      const errorMsg = `❌ setup ~ error: ${error}`;

      this.kafkaProducer.send({
        topic: this.config.KAFKA_TOPIC_STATISTIC_SYSTEM,
        messages: [{ value: errorMsg }],
      });
      logger.error(errorMsg);

      // await this.timeTickService.dropTable();
      throw error;
    }
  }

  public async sumUp(sumUpPayload: SumUpPayload) {
    return await this.influxdbService.sumUp(sumUpPayload);
  }

  public async countUp(sumUpPayload: CountUpPayload) {
    return await this.influxdbService.countUp(sumUpPayload);
  }

  public async getRawData<T>(sumUpPayload: RawDataPayload<T>) {
    return await this.influxdbService.getRawData<T>(sumUpPayload);
  }

  public async getTableGameRawData<T>(sumUpPayload: RawDataPayload<T>) {
    return await this.influxdbService.getTableGameRawData<T>(sumUpPayload);
  }

  public async insertSpinRecords(records: InfluxdbSpinRecordType[]) {
    return await this.clickhouseService.insertSpinRecords(records);
  }

  public async insertTableGameRecords(records: InfluxdbTableRecordType[]) {
    return await this.clickhouseService.insertTableGameRecords(records);
  }

  public async updateProcessingTimeTick(
    taskName: string,
    timeTick: Date,
    direction: STATISTIC_DIRECTION,
    trx?: Knex.Transaction,
  ) {
    await this.timeTickService.updateProcessingTimeTick(taskName, timeTick, direction, trx);
  }

  public async updateProcessedTimeTick(
    taskName: string,
    timeTick: Date,
    direction: STATISTIC_DIRECTION,
    trx?: Knex.Transaction,
  ) {
    await this.timeTickService.updateProcessedTimeTick(taskName, timeTick, direction, trx);
  }

  public shouldProcessBackward(timeTick: TimeTickPayload) {
    const processTickBackward = timeTick.processing_tick_backward || new Date();
    const processTickFirstRecord = timeTick.first_record_tick || new Date();
    return processTickBackward > processTickFirstRecord;
  }

  public getProcessTimeTick(tickName: string, direction: STATISTIC_DIRECTION) {
    return this.timeTickService.getProcessTimeTick(tickName, direction);
  }

  public async dropTimeTick() {
    await this.timeTickService.dropTable();
  }

  public async upSertTotalSumUp(
    influxdbSumUpResults: SumUpResult[] | GranularitySumUp[],
    granularity: Granularity,
    trx?: Knex.Transaction,
  ) {
    await this.totalSumUpService.upsert(influxdbSumUpResults, granularity, trx);
  }

  public async aggregateSumUpByGranularity(payload: AggregateSumUpPayload, trx?: Knex.Transaction) {
    return await this.totalSumUpService.aggregateSumUpByGranularity(payload, trx);
  }

  public async deleteDataInRange(granularity: Granularity, startTime: Date, stopTime: Date, trx?: Knex.Transaction) {
    return await this.totalSumUpService.deleteDataInRange(granularity, startTime, stopTime, trx);
  }

  public async deleteDataInRangeByTableName(
    tableName: ClickhouseTableName,
    startTime: Date,
    stopTime: Date,
    trx?: Knex.Transaction,
  ) {
    return await this.totalSumUpService.deleteDataInRangeByTableName(tableName, startTime, stopTime, trx);
  }

  public async rollbackDataInRange(
    taskName: string,
    direction: STATISTIC_DIRECTION,
    timeTick: ReturnProcessTick,
    startTime: Date,
    stopTime: Date,
    trx?: Knex.Transaction,
  ) {
    try {
      const qc = this.config.QUEUE_SETTINGS.find((q) => q.name === taskName);

      if (!qc) {
        logger.error(`❌ rollbackDataInRange ~ qc not found for ${taskName}`);
        throw new Error(`❌ rollbackDataInRange ~ qc not found for ${taskName}`);
      }

      await this.rollbackTimeTick({
        taskName,
        direction,
        timeTick,
        trx,
      });
    } catch (error) {
      logger.error(
        `${process.pid} ❌ ~ [${taskName}-${direction}] ~ In workerManager ~ rollbackDataInRange ~ error: ${error}`,
      );
      throw error;
    }
  }

  public getConfig() {
    return this.config;
  }

  public getGameService() {
    return this.gameService;
  }

  public async transaction() {
    return await this.totalSumUpService.transaction();
  }

  public async refreshMaterializedView(granularity: Granularity) {
    return await this.totalSumUpService.refreshMaterializedView(granularity);
  }

  public async refreshMaterializedViewByTableNames(tableNames: ClickhouseTableName[]) {
    return await this.totalSumUpService.refreshMaterializedViewByTableNames(tableNames);
  }

  public getSumUpByGranularity(granularity: Granularity, startDate: Date, endDate: Date) {
    return this.totalSumUpService.getSumUpByGranularity(granularity, startDate, endDate);
  }

  public getSumUpTotalGranularityBy(tableName: ClickhouseTableName, startDate: Date, endDate: Date) {
    return this.totalSumUpService.getSumUpTotalGranularityBy(tableName, startDate, endDate);
  }

  public async insertWalletData(walletData: InfluxWalletType[], trx?: Knex.Transaction) {
    return await this.walletDataService.insertWalletData(walletData, trx);
  }

  public async insertWalletHistoryRecords(records: InfluxWalletType[]) {
    return await this.walletHistoryService.insertWalletHistoryRecords('wallet_history', records);
  }

  public async insertTableWalletHistoryRecords(records: InfluxWalletType[]) {
    return await this.walletHistoryService.insertWalletHistoryRecords('table_wallet_history', records);
  }

  public async storeStatisticResult(key: string, results: GranularitySumUpType[], expire: number) {
    await this.preStoreService.storeStatisticResult(key, results, expire);
  }

  public sendKafkaMessage(message: string, topic = this.config.KAFKA_TOPIC_STATISTIC_SYSTEM) {
    this.kafkaProducer.send({
      topic,
      messages: [{ value: message }],
    });
  }

  // notification
  public async getNotificationConfig(name: NotificationName) {
    return await this.notificationService.getNotificationConfig(name);
  }

  public shouldSendNotification(row: BigWinCollector, notificationConfig: NotificationConfig) {
    return this.notificationService.shouldSendNotification(row, notificationConfig);
  }

  public async sendNotification(result: BigWinCollector) {
    const time = dayjs(result.time).tz(this.config.TIMEZONE).format('YYYY-MM-DD HH:mm:ss');
    const game = await this.gameService.getGameById(result.game_id);

    const betAmount = result.bet_amount * 10; // write dead 10x
    const winAmount = result.win_amount * 10; // write dead 10x
    const intlNumber = new Intl.NumberFormat();
    const dashboardUrl = `${this.config.DASHBOARD_URL}/#/risk-audit?search_by=round_id&round_id=${result.round_id}&partner_code=${result.partner_code}&player_id=${result.player_id}&game_id=${result.game_id}`;
    const msg =
      `🔔 <b>!!! Big Win Player !!!</b>` +
      `\n\n<b>Round:</b> ${result.round_id}` +
      `\n<b>Round Type:</b> ${result.round_type}` +
      `\n<b>Partner Code:</b> ${result.partner_code}` +
      `\n<b>Player ID:</b> ${result.player_id}` +
      `\n<b>Game:</b> ${game?.name ? game.name : result.game_id}` +
      `\n<b>Bet Amount:</b> ${intlNumber.format(betAmount)}` +
      `\n<b>Win Amount:</b> ${intlNumber.format(winAmount)}` +
      `\n<b>Multiplier:</b> ${betAmount !== 0 ? winAmount / betAmount : 0}` +
      (this.config.debug ? `\n<b>Spin IDs:</b> ${result.spin_ids?.join(', ')}` : '') +
      `\n<b>Time:</b> ${time}`;

    logger.info(`${process.pid} 🚀 [processNotification] ~ msg: ${msg}`);
    this.sendKafkaMessage(
      JSON.stringify({ message: msg, parse_mode: 'HTML', url: dashboardUrl }),
      this.config.KAFKA_TOPIC_STATISTIC_RISK,
    );
  }

  private async rollbackTimeTick({
    taskName,
    direction,
    timeTick,
    trx,
  }: {
    taskName: string;
    direction: STATISTIC_DIRECTION;
    timeTick: ReturnProcessTick;
    trx?: Knex.Transaction;
  }) {
    const rollbackTimeTick =
      direction === STATISTIC_DIRECTION.NEXT ? timeTick.processedTickNext : timeTick.processedTickBackward;

    // prevent null value cause toISOString error
    if (!rollbackTimeTick) {
      const errorMsg = `❌ rollbackTimeTick is null for ${taskName}-${direction}, using systemStartTick as fallback`;
      logger.error(errorMsg);
      const fallbackTick = timeTick.systemStartTick;

      // Update both processing and processed ticks to ensure consistency
      await this.updateProcessingTimeTick(taskName as QUEUE_NAME, fallbackTick, direction as STATISTIC_DIRECTION, trx);
      await this.updateProcessedTimeTick(taskName as QUEUE_NAME, fallbackTick, direction as STATISTIC_DIRECTION, trx);

      logger.info(
        `${
          process.pid
        } 🥵 ~ [${taskName}-${direction}] ~ In workerManager ~ rollback the time tick (${fallbackTick.toISOString()}) [fallback] and sync processed tick`,
      );
      return;
    }

    await this.updateProcessingTimeTick(
      taskName as QUEUE_NAME,
      rollbackTimeTick,
      direction as STATISTIC_DIRECTION,
      trx,
    );

    logger.info(
      `${
        process.pid
      } 🥵 ~ [${taskName}-${direction}] ~ In workerManager ~ rollback the time tick (${rollbackTimeTick.toISOString()})`,
    );
  }
}
