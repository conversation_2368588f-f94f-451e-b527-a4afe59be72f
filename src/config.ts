import { config } from 'dotenv';
import { STATISTIC_DIRECTION } from './handlers/jobHandler';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { CronRepeatOptions, EveryRepeatOptions } from 'bull';
import ms from 'ms';
import { InfluxTableName } from './db';
import logger from './logger';

// config env
config();

dayjs.extend(timezone);
dayjs.extend(utc);

export interface Config {
  DASHBOARD_URL: string;
  REDIS_URL: string;
  DB_URL_MASTER: string;
  DB_TYPE: string;
  CLICKHOUSE_URL: string;
  CLICKHOUSE_DATABASE: string;
  CLICKHOUSE_USER: string;
  CLICKHOUSE_PASSWORD: string;
  QUEUE_SETTINGS: QueueConfig[];
  SOURCE_INFLUXDB_URL: string;
  SOURCE_INFLUXDB_SPIN_MEASUREMENT: string;
  SOURCE_INFLUXDB_WALLET_MEASUREMENT: string;
  SOURCE_INFLUXDB_TOKEN: string;
  SOURCE_INFLUXDB_ORG: string;
  SOURCE_INFLUXDB_BUCKET: string;
  SOURCE_INFLUXDB_CONNECTION_TIMEOUT: number;

  SOURCE_INFLUXDB_TABLE_URL: string;
  SOURCE_INFLUXDB_TABLE_BUCKET: string;
  SOURCE_INFLUXDB_TABLE_MEASUREMENT: string;
  SOURCE_INFLUXDB_TABLE_TOKEN: string;
  SOURCE_INFLUXDB_TABLE_ORG: string;

  TIMEZONE: string;
  MANUAL_FIRST_TIME_TICK: Date | null;

  KAFKA_CLIENT_ID: string;
  KAFKA_CONSUMER_GROUP_ID: string;
  KAFKA_BROKERS: string[];
  KAFKA_TOPIC_STATISTIC_SYSTEM: string;
  KAFKA_TOPIC_STATISTIC_RISK: string;
  KAFKA_TOPIC_TOTAL_SUM_UP: string;

  HEALTHY_CHECK_NOTIFY: boolean;
  debug: boolean;
}

export enum QUEUE_NAME {
  FIRST_TIME_TICK = 'first_time_tick',
  SPIN_HISTORY = 'spin-history',
  WALLET_HISTORY = 'wallet-history',
  TABLE_HISTORY = 'table-history',
  TABLE_WALLET_HISTORY = 'table-wallet-history',

  // store the wallet history raw data.
  WALLET_DATA_STORE = 'wallet-data-store',
  // store the spin history raw data.
  SPIN_DATA_STORE = 'spin-data-store',
  // store the table history raw data.
  TABLE_DATA_STORE = 'table-data-store',

  GAME_DISTRIBUTION_MUL = 'game-distribution-mul',
  STORE_TO_REDIS = 'store-to-redis',
}

export enum WorkerName {
  SpinHistoryWorker = 'spin-history-worker',
  WalletHistoryWorker = 'wallet-history-worker',
  TableHistoryWorker = 'table-history-worker',
  TableWalletHistoryWorker = 'table-wallet-history-worker',
  GameDistributionWorker = 'game-distribution-worker',
  PrestoreWorker = 'prestore-worker',
}

export type Cron = CronRepeatOptions | EveryRepeatOptions;

export interface DirectionCron {
  [STATISTIC_DIRECTION.BACKWARD]: Cron;
  [STATISTIC_DIRECTION.NEXT]: Cron;
}

interface PossibleCron {
  [key: string]: unknown;
  cron?: string;
  [STATISTIC_DIRECTION.BACKWARD]?: Cron;
  [STATISTIC_DIRECTION.NEXT]?: Cron;
}

type GeneralQueue = {
  name: string;
  workerName: WorkerName;
  dbType: string;
  dbUrlMaster: string;

  // clickhouse
  clickhouseUrl: string;
  clickhouseDatabase: string;
  clickhouseUser: string;
  clickhousePassword: string;

  // game data influxdb
  sourceInfluxdbUrl: string;
  sourceInfluxdbMeasurement: InfluxTableName;
  sourceInfluxdbToken: string;
  sourceInfluxdbOrg: string;
  sourceInfluxdbBucket: string;
  sourceInfluxdbConnectionTimeout: number;

  // redis
  redisUrl: string;
};

export type QueueConfig = GeneralQueue & {
  statisticBackwardRange: number;
  statisticNextRange: number;
  repeat: DirectionCron;
  shouldSpawnBackward: boolean;
};

type ExtendSettings = {
  name: string;
  url: string;
}[];

const parseAsExtendSettings = (extendSettings: string): ExtendSettings => {
  try {
    const extendSettingsObj = JSON.parse(extendSettings) as ExtendSettings;

    if (!Array.isArray(extendSettingsObj)) {
      logger.error(`Invalid extend settings: ${extendSettings}`);
      throw new Error(`Invalid extend settings: ${extendSettings}`);
    }

    if (extendSettingsObj.some((setting) => !setting.name || !setting.url)) {
      logger.error(`Invalid extend settings: ${extendSettings}`);
      throw new Error(`Invalid extend settings: ${extendSettings}`);
    }

    return extendSettingsObj;
  } catch (error) {
    logger.error(`Invalid extend settings: ${extendSettings}`);
    throw new Error(`Invalid extend settings: ${extendSettings}`);
  }
};

const generateSpinExtendQueueConfig = (extendSettings: string): QueueConfig[] => {
  if (extendSettings === '') return [];

  const extendSettingsObj = parseAsExtendSettings(extendSettings);
  return extendSettingsObj
    .filter((setting) => setting.url !== '')
    .map((setting) => {
      return [
        // spin data
        {
          name: `${QUEUE_NAME.SPIN_DATA_STORE}_${setting.name}`,
          workerName: WorkerName.SpinHistoryWorker,
          repeat: {
            backward: {
              every: ms(process.env.SPIN_DATA_STORE_EVERY || '15s'),
            },
            next: {
              every: ms(process.env.SPIN_DATA_STORE_EVERY || '15s'),
            },
          },
          statisticBackwardRange: Number(process.env.SPIN_DATA_TIME_RANGE) || 1, // 1 minute per loop
          statisticNextRange: Number(process.env.SPIN_DATA_TIME_RANGE) || 1, // 1 minute per loop

          dbType: process.env.DB_TYPE || 'pg',
          dbUrlMaster:
            process.env.DB_URL_MASTER || 'postgresql://customuser:<EMAIL>:5432/customdb',

          // clickhouse
          clickhouseUrl: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
          clickhouseDatabase: process.env.CLICKHOUSE_DATABASE || 'customdb',
          clickhouseUser: process.env.CLICKHOUSE_USER || 'customdb',
          clickhousePassword: process.env.CLICKHOUSE_PASSWORD || 'custompassword',

          // game data influxdb
          sourceInfluxdbUrl: setting.url,
          sourceInfluxdbMeasurement:
            (process.env.SOURCE_INFLUXDB_SPIN_MEASUREMENT as InfluxTableName) || InfluxTableName.spin_history,
          sourceInfluxdbToken: process.env.SOURCE_INFLUXDB_TOKEN || 'thisisatempass',
          sourceInfluxdbOrg: process.env.SOURCE_INFLUXDB_ORG || 'edge-slot',
          sourceInfluxdbBucket: process.env.SOURCE_INFLUXDB_BUCKET || 'game-data',
          sourceInfluxdbConnectionTimeout: Number(process.env.SOURCE_INFLUXDB_CONNECTION_TIMEOUT) || 900000,

          redisUrl: process.env.REDIS_URL || 'redis://host.docker.internal:6379',

          shouldSpawnBackward: true,
        },
        // wallet data
        {
          name: `${QUEUE_NAME.WALLET_HISTORY}_${setting.name}`,
          workerName: WorkerName.WalletHistoryWorker,
          repeat: {
            backward: {
              every: ms(process.env.WALLET_DATA_STORE_EVERY || '1s'),
            },
            next: {
              every: ms(process.env.WALLET_DATA_STORE_EVERY || '1s'),
            },
          },

          statisticBackwardRange: Number(process.env.WALLET_HISTORY_TIME_RANGE) || 60, // 60 minute
          statisticNextRange: Number(process.env.WALLET_HISTORY_TIME_RANGE) || 60, // 60 minute

          dbType: process.env.DB_TYPE || 'pg',
          dbUrlMaster:
            process.env.DB_URL_MASTER || 'postgresql://customuser:<EMAIL>:5432/customdb',

          // clickhouse
          clickhouseUrl: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
          clickhouseDatabase: process.env.CLICKHOUSE_DATABASE || 'customdb',
          clickhouseUser: process.env.CLICKHOUSE_USER || 'customdb',
          clickhousePassword: process.env.CLICKHOUSE_PASSWORD || 'custompassword',

          // game data influxdb
          sourceInfluxdbUrl: setting.url,
          sourceInfluxdbMeasurement:
            (process.env.SOURCE_INFLUXDB_WALLET_MEASUREMENT as InfluxTableName) || InfluxTableName.wallet_history,
          sourceInfluxdbToken: process.env.SOURCE_INFLUXDB_TOKEN || 'thisisatempass',
          sourceInfluxdbOrg: process.env.SOURCE_INFLUXDB_ORG || 'edge-slot',
          sourceInfluxdbBucket: process.env.SOURCE_INFLUXDB_BUCKET || 'game-data',
          sourceInfluxdbConnectionTimeout: Number(process.env.SOURCE_INFLUXDB_CONNECTION_TIMEOUT) || 900000,

          redisUrl: process.env.REDIS_URL || 'redis://host.docker.internal:6379',

          shouldSpawnBackward: true,
        },
      ];
    })
    .flat();
};

const generateTableExtendQueueConfig = (extendSettings: string): QueueConfig[] => {
  if (extendSettings === '') return [];

  const extendSettingsObj = parseAsExtendSettings(extendSettings);

  return extendSettingsObj
    .filter((setting) => setting.url !== '')
    .map((setting) => {
      return [
        // table data
        {
          name: `${QUEUE_NAME.TABLE_DATA_STORE}_${setting.name}`,
          workerName: WorkerName.TableHistoryWorker,
          repeat: {
            backward: {
              every: ms(process.env.TABLE_DATA_STORE_EVERY || '15s'),
            },
            next: {
              every: ms(process.env.TABLE_DATA_STORE_EVERY || '15s'),
            },
          },
          statisticBackwardRange: Number(process.env.TABLE_DATA_TIME_RANGE) || 1, // 1 minute per loop
          statisticNextRange: Number(process.env.TABLE_DATA_TIME_RANGE) || 1, // 1 minute per loop

          dbType: process.env.DB_TYPE || 'pg',
          dbUrlMaster:
            process.env.DB_URL_MASTER || 'postgresql://customuser:<EMAIL>:5432/customdb',

          // clickhouse
          clickhouseUrl: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
          clickhouseDatabase: process.env.CLICKHOUSE_DATABASE || 'customdb',
          clickhouseUser: process.env.CLICKHOUSE_USER || 'customdb',
          clickhousePassword: process.env.CLICKHOUSE_PASSWORD || 'custompassword',

          // game data influxdb
          sourceInfluxdbUrl: setting.url,
          sourceInfluxdbMeasurement:
            (process.env.SOURCE_INFLUXDB_TABLE_MEASUREMENT as InfluxTableName) || InfluxTableName.table_history,
          sourceInfluxdbToken: process.env.SOURCE_INFLUXDB_TABLE_TOKEN || 'thisisatempass',
          sourceInfluxdbOrg: process.env.SOURCE_INFLUXDB_TABLE_ORG || 'edge-table',
          sourceInfluxdbBucket: process.env.SOURCE_INFLUXDB_TABLE_BUCKET || 'table-game-data',
          sourceInfluxdbConnectionTimeout: Number(process.env.SOURCE_INFLUXDB_CONNECTION_TIMEOUT) || 900000,

          redisUrl: process.env.REDIS_URL || 'redis://host.docker.internal:6379',

          shouldSpawnBackward: true,
        },
        // table wallet data
        {
          name: `${QUEUE_NAME.TABLE_WALLET_HISTORY}_${setting.name}`,
          workerName: WorkerName.TableWalletHistoryWorker,
          repeat: {
            backward: {
              every: ms(process.env.TABLE_WALLET_DATA_STORE_EVERY || '1s'),
            },
            next: {
              every: ms(process.env.TABLE_WALLET_DATA_STORE_EVERY || '1s'),
            },
          },
          statisticBackwardRange: Number(process.env.TABLE_WALLET_HISTORY_TIME_RANGE) || 1, // 1 minute
          statisticNextRange: Number(process.env.TABLE_WALLET_HISTORY_TIME_RANGE) || 1, // 1 minute

          dbType: process.env.DB_TYPE || 'pg',
          dbUrlMaster:
            process.env.DB_URL_MASTER || 'postgresql://customuser:<EMAIL>:5432/customdb',

          // clickhouse
          clickhouseUrl: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
          clickhouseDatabase: process.env.CLICKHOUSE_DATABASE || 'customdb',
          clickhouseUser: process.env.CLICKHOUSE_USER || 'customdb',
          clickhousePassword: process.env.CLICKHOUSE_PASSWORD || 'custompassword',

          // game data influxdb
          sourceInfluxdbUrl: setting.url,
          sourceInfluxdbMeasurement:
            (process.env.SOURCE_INFLUXDB_WALLET_MEASUREMENT as InfluxTableName) || InfluxTableName.wallet_history,
          sourceInfluxdbToken: process.env.SOURCE_INFLUXDB_TABLE_TOKEN || 'thisisatempass',
          sourceInfluxdbOrg: process.env.SOURCE_INFLUXDB_TABLE_ORG || 'edge-table',
          sourceInfluxdbBucket: process.env.SOURCE_INFLUXDB_TABLE_BUCKET || 'table-game-data',
          sourceInfluxdbConnectionTimeout: Number(process.env.SOURCE_INFLUXDB_CONNECTION_TIMEOUT) || 900000,

          redisUrl: process.env.REDIS_URL || 'redis://host.docker.internal:6379',

          shouldSpawnBackward: true,
        },
      ];
    })
    .flat();
};

const generateQueueConfig = (): QueueConfig[] => {
  const baseQueueConfigs: QueueConfig[] = [
    // wallet data
    {
      name: QUEUE_NAME.WALLET_HISTORY,
      workerName: WorkerName.WalletHistoryWorker,
      repeat: {
        backward: {
          every: ms(process.env.WALLET_DATA_STORE_EVERY || '1s'),
        },
        next: {
          every: ms(process.env.WALLET_DATA_STORE_EVERY || '1s'),
        },
      },

      statisticBackwardRange: Number(process.env.WALLET_HISTORY_TIME_RANGE) || 60, // 60 minute
      statisticNextRange: Number(process.env.WALLET_HISTORY_TIME_RANGE) || 60, // 60 minute

      dbType: process.env.DB_TYPE || 'pg',
      dbUrlMaster:
        process.env.DB_URL_MASTER || 'postgresql://customuser:<EMAIL>:5432/customdb',

      // clickhouse
      clickhouseUrl: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
      clickhouseDatabase: process.env.CLICKHOUSE_DATABASE || 'customdb',
      clickhouseUser: process.env.CLICKHOUSE_USER || 'customdb',
      clickhousePassword: process.env.CLICKHOUSE_PASSWORD || 'custompassword',

      // game data influxdb
      sourceInfluxdbUrl: process.env.SOURCE_INFLUXDB_URL || 'http://localhost:8086',
      sourceInfluxdbMeasurement:
        (process.env.SOURCE_INFLUXDB_WALLET_MEASUREMENT as InfluxTableName) || InfluxTableName.wallet_history,
      sourceInfluxdbToken: process.env.SOURCE_INFLUXDB_TOKEN || 'thisisatempass',
      sourceInfluxdbOrg: process.env.SOURCE_INFLUXDB_ORG || 'edge-slot',
      sourceInfluxdbBucket: process.env.SOURCE_INFLUXDB_BUCKET || 'game-data',
      sourceInfluxdbConnectionTimeout: Number(process.env.SOURCE_INFLUXDB_CONNECTION_TIMEOUT) || 900000,

      redisUrl: process.env.REDIS_URL || 'redis://host.docker.internal:6379',

      shouldSpawnBackward: true,
    },
    // table wallet data
    {
      name: QUEUE_NAME.TABLE_WALLET_HISTORY,
      workerName: WorkerName.TableWalletHistoryWorker,
      repeat: {
        backward: {
          every: ms(process.env.TABLE_WALLET_DATA_STORE_EVERY || '1s'),
        },
        next: {
          every: ms(process.env.TABLE_WALLET_DATA_STORE_EVERY || '1s'),
        },
      },
      statisticBackwardRange: Number(process.env.TABLE_WALLET_HISTORY_TIME_RANGE) || 1, // 1 minute
      statisticNextRange: Number(process.env.TABLE_WALLET_HISTORY_TIME_RANGE) || 1, // 1 minute

      dbType: process.env.DB_TYPE || 'pg',
      dbUrlMaster:
        process.env.DB_URL_MASTER || 'postgresql://customuser:<EMAIL>:5432/customdb',

      // clickhouse
      clickhouseUrl: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
      clickhouseDatabase: process.env.CLICKHOUSE_DATABASE || 'customdb',
      clickhouseUser: process.env.CLICKHOUSE_USER || 'customdb',
      clickhousePassword: process.env.CLICKHOUSE_PASSWORD || 'custompassword',

      // game data influxdb
      sourceInfluxdbUrl: process.env.SOURCE_INFLUXDB_TABLE_URL || 'http://localhost:8086',
      sourceInfluxdbMeasurement:
        (process.env.SOURCE_INFLUXDB_WALLET_MEASUREMENT as InfluxTableName) || InfluxTableName.wallet_history,
      sourceInfluxdbToken: process.env.SOURCE_INFLUXDB_TABLE_TOKEN || 'thisisatempass',
      sourceInfluxdbOrg: process.env.SOURCE_INFLUXDB_TABLE_ORG || 'edge-table',
      sourceInfluxdbBucket: process.env.SOURCE_INFLUXDB_TABLE_BUCKET || 'table-game-data',
      sourceInfluxdbConnectionTimeout: Number(process.env.SOURCE_INFLUXDB_CONNECTION_TIMEOUT) || 900000,

      redisUrl: process.env.REDIS_URL || 'redis://host.docker.internal:6379',

      shouldSpawnBackward: true,
    },
    // spin data
    {
      name: QUEUE_NAME.SPIN_DATA_STORE,
      workerName: WorkerName.SpinHistoryWorker,
      repeat: {
        backward: {
          every: ms(process.env.SPIN_DATA_STORE_EVERY || '15s'),
        },
        next: {
          every: ms(process.env.SPIN_DATA_STORE_EVERY || '15s'),
        },
      },
      statisticBackwardRange: Number(process.env.SPIN_DATA_TIME_RANGE) || 1, // 1 minute per loop
      statisticNextRange: Number(process.env.SPIN_DATA_TIME_RANGE) || 1, // 1 minute per loop

      dbType: process.env.DB_TYPE || 'pg',
      dbUrlMaster:
        process.env.DB_URL_MASTER || 'postgresql://customuser:<EMAIL>:5432/customdb',

      // clickhouse
      clickhouseUrl: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
      clickhouseDatabase: process.env.CLICKHOUSE_DATABASE || 'customdb',
      clickhouseUser: process.env.CLICKHOUSE_USER || 'customdb',
      clickhousePassword: process.env.CLICKHOUSE_PASSWORD || 'custompassword',

      // game data influxdb
      sourceInfluxdbUrl: process.env.SOURCE_INFLUXDB_URL || 'http://localhost:8086',
      sourceInfluxdbMeasurement:
        (process.env.SOURCE_INFLUXDB_SPIN_MEASUREMENT as InfluxTableName) || InfluxTableName.spin_history,
      sourceInfluxdbToken: process.env.SOURCE_INFLUXDB_TOKEN || 'thisisatempass',
      sourceInfluxdbOrg: process.env.SOURCE_INFLUXDB_ORG || 'edge-slot',
      sourceInfluxdbBucket: process.env.SOURCE_INFLUXDB_BUCKET || 'game-data',
      sourceInfluxdbConnectionTimeout: Number(process.env.SOURCE_INFLUXDB_CONNECTION_TIMEOUT) || 900000,

      redisUrl: process.env.REDIS_URL || 'redis://host.docker.internal:6379',

      shouldSpawnBackward: true,
    },
    // table data
    {
      name: QUEUE_NAME.TABLE_DATA_STORE,
      workerName: WorkerName.TableHistoryWorker,
      repeat: {
        backward: {
          every: ms(process.env.TABLE_DATA_STORE_EVERY || '15s'),
        },
        next: {
          every: ms(process.env.TABLE_DATA_STORE_EVERY || '15s'),
        },
      },
      statisticBackwardRange: Number(process.env.TABLE_DATA_TIME_RANGE) || 1, // 1 minute per loop
      statisticNextRange: Number(process.env.TABLE_DATA_TIME_RANGE) || 1, // 1 minute per loop

      dbType: process.env.DB_TYPE || 'pg',
      dbUrlMaster:
        process.env.DB_URL_MASTER || 'postgresql://customuser:<EMAIL>:5432/customdb',

      // clickhouse
      clickhouseUrl: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
      clickhouseDatabase: process.env.CLICKHOUSE_DATABASE || 'customdb',
      clickhouseUser: process.env.CLICKHOUSE_USER || 'customdb',
      clickhousePassword: process.env.CLICKHOUSE_PASSWORD || 'custompassword',

      // game data influxdb
      sourceInfluxdbUrl: process.env.SOURCE_INFLUXDB_TABLE_URL || 'http://localhost:8086',
      sourceInfluxdbMeasurement:
        (process.env.SOURCE_INFLUXDB_TABLE_MEASUREMENT as InfluxTableName) || InfluxTableName.table_history,
      sourceInfluxdbToken: process.env.SOURCE_INFLUXDB_TABLE_TOKEN || 'thisisatempass',
      sourceInfluxdbOrg: process.env.SOURCE_INFLUXDB_TABLE_ORG || 'edge-table',
      sourceInfluxdbBucket: process.env.SOURCE_INFLUXDB_TABLE_BUCKET || 'table-game-data',
      sourceInfluxdbConnectionTimeout: Number(process.env.SOURCE_INFLUXDB_CONNECTION_TIMEOUT) || 900000,

      redisUrl: process.env.REDIS_URL || 'redis://host.docker.internal:6379',

      shouldSpawnBackward: true,
    },
    // game distribution mul
    {
      name: QUEUE_NAME.GAME_DISTRIBUTION_MUL,
      workerName: WorkerName.GameDistributionWorker,
      repeat: {
        backward: {
          every: ms('5s'), // nop
        },
        next: {
          every: ms(process.env.GAME_DISTRIBUTION_MUL_EVERY || '5s'),
        },
      },
      statisticBackwardRange: 1, // nop
      statisticNextRange: 1, // nop

      dbType: process.env.DB_TYPE || 'pg',
      dbUrlMaster:
        process.env.DB_URL_MASTER || 'postgresql://customuser:<EMAIL>:5432/customdb',

      // clickhouse
      clickhouseUrl: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
      clickhouseDatabase: process.env.CLICKHOUSE_DATABASE || 'customdb',
      clickhouseUser: process.env.CLICKHOUSE_USER || 'customdb',
      clickhousePassword: process.env.CLICKHOUSE_PASSWORD || 'custompassword',
      // game data influxdb
      sourceInfluxdbUrl: process.env.SOURCE_INFLUXDB_URL || 'http://localhost:8086',
      sourceInfluxdbMeasurement:
        (process.env.SOURCE_INFLUXDB_SPIN_MEASUREMENT as InfluxTableName) || InfluxTableName.spin_history,
      sourceInfluxdbToken: process.env.SOURCE_INFLUXDB_TOKEN || 'thisisatempass',
      sourceInfluxdbOrg: process.env.SOURCE_INFLUXDB_ORG || 'edge-slot',
      sourceInfluxdbBucket: process.env.SOURCE_INFLUXDB_BUCKET || 'game-data',
      sourceInfluxdbConnectionTimeout: Number(process.env.SOURCE_INFLUXDB_CONNECTION_TIMEOUT) || 900000,
      redisUrl: process.env.REDIS_URL || 'redis://host.docker.internal:6379',
      shouldSpawnBackward: false,
    },
  ];

  const spinExtendQueueConfigs = generateSpinExtendQueueConfig(process.env.SOURCE_INFLUXDB_EXTENDS || '');
  const tableExtendQueueConfigs = generateTableExtendQueueConfig(process.env.SOURCE_INFLUXDB_TABLE_EXTENDS || '');

  return [...baseQueueConfigs, ...spinExtendQueueConfigs, ...tableExtendQueueConfigs];
};

export function createConfig(): Config {
  const kafkaBrokers = process.env.KAFKA_BROKERS?.split(',') || undefined;

  return {
    debug: process.env.DEBUG === 'true',

    // dashboard
    DASHBOARD_URL: process.env.DASHBOARD_URL || 'http://localhost:4000',

    // database
    DB_TYPE: process.env.DB_TYPE || 'pg',
    DB_URL_MASTER:
      process.env.DB_URL_MASTER || 'postgresql://customuser:<EMAIL>:5432/customdb',

    // clickhouse
    CLICKHOUSE_URL: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
    CLICKHOUSE_DATABASE: process.env.CLICKHOUSE_DATABASE || 'customdb',
    CLICKHOUSE_USER: process.env.CLICKHOUSE_USER || 'customdb',
    CLICKHOUSE_PASSWORD: process.env.CLICKHOUSE_PASSWORD || 'custompassword',

    // game data influxdb
    SOURCE_INFLUXDB_URL: process.env.SOURCE_INFLUXDB_URL || 'http://localhost:8086',
    SOURCE_INFLUXDB_SPIN_MEASUREMENT: process.env.SOURCE_INFLUXDB_SPIN_MEASUREMENT || InfluxTableName.spin_history,
    SOURCE_INFLUXDB_WALLET_MEASUREMENT:
      process.env.SOURCE_INFLUXDB_WALLET_MEASUREMENT || InfluxTableName.wallet_history,
    SOURCE_INFLUXDB_TOKEN: process.env.SOURCE_INFLUXDB_TOKEN || 'thisisatempass',
    SOURCE_INFLUXDB_ORG: process.env.SOURCE_INFLUXDB_ORG || 'edge-slot',
    SOURCE_INFLUXDB_BUCKET: process.env.SOURCE_INFLUXDB_BUCKET || 'game-data',
    SOURCE_INFLUXDB_CONNECTION_TIMEOUT: Number(process.env.SOURCE_INFLUXDB_CONNECTION_TIMEOUT) || 900000,

    SOURCE_INFLUXDB_TABLE_URL: process.env.SOURCE_INFLUXDB_TABLE_URL || 'http://localhost:8086',
    SOURCE_INFLUXDB_TABLE_MEASUREMENT: process.env.SOURCE_INFLUXDB_TABLE_MEASUREMENT || InfluxTableName.table_history,
    SOURCE_INFLUXDB_TABLE_TOKEN: process.env.SOURCE_INFLUXDB_TABLE_TOKEN || 'thisisatempass',
    SOURCE_INFLUXDB_TABLE_ORG: process.env.SOURCE_INFLUXDB_TABLE_ORG || 'edge-table',
    SOURCE_INFLUXDB_TABLE_BUCKET: process.env.SOURCE_INFLUXDB_TABLE_BUCKET || 'table-game-data',

    // redis
    REDIS_URL: process.env.REDIS_URL || 'redis://host.docker.internal:6379',

    // kafka
    KAFKA_CLIENT_ID: process.env.KAFKA_CLIENT_ID || 'bot-service',
    KAFKA_CONSUMER_GROUP_ID: process.env.KAFKA_CONSUMER_GROUP_ID || 'bot-service',
    KAFKA_BROKERS: kafkaBrokers || ['localhost:9092'],
    KAFKA_TOPIC_STATISTIC_SYSTEM: process.env.KAFKA_TOPIC_STATISTIC_SYSTEM || 'statistic-system',
    KAFKA_TOPIC_STATISTIC_RISK: process.env.KAFKA_TOPIC_STATISTIC_RISK || 'statistic-risk',
    KAFKA_TOPIC_TOTAL_SUM_UP: process.env.KAFKA_TOPIC_TOTAL_SUM_UP || 'kafka-total-sum-up',

    // jackpot multiple notify
    HEALTHY_CHECK_NOTIFY: process.env.HEALTHY_CHECK_NOTIFY === 'true',

    // system config
    MANUAL_FIRST_TIME_TICK: process.env.MANUAL_FIRST_TIME_TICK ? new Date(process.env.MANUAL_FIRST_TIME_TICK) : null,

    // timezone (default +7)
    TIMEZONE: process.env.TIMEZONE || 'Asia/Jakarta',

    // queue name
    QUEUE_SETTINGS: generateQueueConfig(),
  };
}

export const getQueueConfig = (taskName: string) => {
  const config = createConfig();
  const queueConfig = config.QUEUE_SETTINGS.find((qc) => qc.name === taskName);

  if (!queueConfig) throw new Error(`no queue config found for ${taskName}!`);

  return queueConfig;
};

export const getTimezone = () => {
  const config = createConfig();
  return config.TIMEZONE || undefined;
};

export const getTimezoneOffset = () => {
  const config = createConfig();
  return config.TIMEZONE ? dayjs().tz(config.TIMEZONE).utcOffset() / 60 : 0;
};

function isDirectionCron(cron: unknown): cron is DirectionCron {
  const obj = cron as PossibleCron;
  return Boolean(obj[STATISTIC_DIRECTION.BACKWARD] && obj[STATISTIC_DIRECTION.NEXT]);
}

export const getRepeatCron = (queueName: string, direction: STATISTIC_DIRECTION) => {
  const queue = createConfig().QUEUE_SETTINGS.find((qc) => qc.name === queueName);

  if (!queue) throw new Error(`queue ${queueName} not found`);

  return isDirectionCron(queue.repeat) ? queue.repeat[direction] : queue.repeat;
};
