import Redis from 'ioredis';
import logger from '../logger';

export const createRedisClient = (redisUrl: string) => {
  // 根據進程類型設置不同的連接配置
  const isMaster = process.env.QUEUE_NAME === undefined;

  const client = new Redis(redisUrl, {
    maxRetriesPerRequest: 3,
    lazyConnect: true, // 延遲連接，減少啟動時的連接競爭
    keepAlive: 30000,
    connectTimeout: 10000,
    commandTimeout: 5000,
    // Master 需要更多連接處理任務調度，Worker 只需要基本連接
    maxLoadingRetryTime: isMaster ? 10000 : 5000,
  });

  client.on('error', (err) => console.error('Redis Client Error', err));
  client.on('connect', () =>
    logger.info(`Redis connected for ${isMaster ? 'Master' : 'Worker'} process ${process.pid}`),
  );

  return client;
};

export const REDIS_KEY = {
  sum_up_min: 'sum_up_min',
  sum_up_hourly: 'sum_up_hourly',
  sum_up_daily: 'sum_up_daily',
  sum_up_monthly: 'sum_up_monthly',
  sum_up_no_player_min: 'sum_up_no_player_min',
  sum_up_no_player_hourly: 'sum_up_no_player_hourly',
  sum_up_no_player_daily: 'sum_up_no_player_daily',
  sum_up_no_player_monthly: 'sum_up_no_player_monthly',

  sum_up_total_game_daily: 'sum_up_total_game_daily',
  sum_up_total_game_monthly: 'sum_up_total_game_monthly',
  sum_up_total_partner_daily: 'sum_up_total_partner_daily',
  sum_up_total_partner_monthly: 'sum_up_total_partner_monthly',
  sum_up_total_all_monthly: 'sum_up_total_all_monthly',
};
