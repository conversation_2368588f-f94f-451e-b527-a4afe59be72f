import knex from 'knex';
import logger from '../logger';

export const bootstrapDB = (dbUrl: string, dbType: string, debug: boolean = false) => {
  logger.info(`${process.pid} 👉 ~ file: index.ts:5 ~ bootstrapDB ~ dbUrl: ${dbUrl}`);

  // 根據進程類型設置不同的連接池大小
  const isMaster = process.env.QUEUE_NAME === undefined;
  const poolConfig = isMaster
    ? { min: 2, max: 20 } // Master: 較大連接池，處理調度
    : { min: 1, max: 8 }; // Worker: 較小連接池，專注處理

  return knex({
    client: dbType,
    version: '7.2',
    connection: dbUrl,
    debug,
    pool: {
      ...poolConfig,
      acquireTimeoutMillis: 30000,
      createTimeoutMillis: 30000,
      destroyTimeoutMillis: 5000,
      idleTimeoutMillis: 30000,
    },
  });
};
