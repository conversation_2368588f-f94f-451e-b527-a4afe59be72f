import knex from 'knex';
import logger from '../logger';

export const bootstrapDB = (dbUrl: string, dbType: string, debug: boolean = false) => {
  logger.info(`${process.pid} 👉 ~ file: index.ts:5 ~ bootstrapDB ~ dbUrl: ${dbUrl}`);

  // 根據進程類型設置不同的連接池大小
  const isMaster = process.env.QUEUE_NAME === undefined;
  const poolConfig = isMaster
    ? { min: 1, max: 8 } // Master: tinier pool for master process, majorly for task scheduling
    : { min: 2, max: 10 }; // Worker: larger pool for worker processes. Processing large amount of data

  return knex({
    client: dbType,
    version: '7.2',
    connection: dbUrl,
    debug,
    pool: {
      ...poolConfig,
      acquireTimeoutMillis: 30000,
      createTimeoutMillis: 30000,
      destroyTimeoutMillis: 5000,
      idleTimeoutMillis: 30000,
    },
  });
};
