import { createClient } from '@clickhouse/client';
import logger from '../logger';

export const bootstrapClickhouse = ({
  url,
  username,
  password,
  database,
}: {
  url: string;
  username: string;
  password: string;
  database: string;
}) => {
  logger.info(
    `${process.pid} 👉 ~ file: index.ts:5 ~ bootstrapDB createClickhouseClient: ~ dbUrl: ${url}; username: ${username}; database: ${database}`,
  );

  try {
    // set different max_connections for master and worker
    const isMaster = process.env.QUEUE_NAME === undefined;
    const maxConnections = isMaster ? 5 : 10; // Master: 5 connections, Worker: 10 connections

    const client = createClient({
      url,
      username,
      password,
      database,
      max_open_connections: maxConnections,
      request_timeout: 60000,
      clickhouse_settings: {
        date_time_input_format: 'best_effort',
        max_execution_time: 30,
      },
    });

    return client;
  } catch (error) {
    logger.error(`❌ [${process.pid}] createClickhouseClient ~ error: ${error}`);
    throw error;
  }
};
