import { Kafka } from 'kafkajs';
import logger from '../logger';

export async function createKafka({
  clientId,
  brokers,
  groupId,
}: {
  clientId: string;
  brokers: string[];
  groupId: string;
}) {
  try {
    const kafka = new Kafka({
      clientId,
      brokers,
    });

    const producer = kafka.producer();
    await producer.connect();

    const consumer = kafka.consumer({ groupId });
    await consumer.connect();

    return {
      consumer,
      producer,
    };
  } catch (error) {
    logger.error(`❌ createKafka ~ error: ${error}`);
    throw error;
  }
}
