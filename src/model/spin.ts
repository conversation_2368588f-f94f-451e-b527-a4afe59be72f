export type SpinRecord = {
  game_id: string;
  partner_code: string;
  player_id: string;
  round_type: string;
  balance_after: number;
  time: string; // should parse to ISO string
  balance_before: number;
  bet_amount: number;
  is_free_rounds: boolean;
  is_free_spins: boolean;
  round_id: string;
  spin_id: string;
  state: string;
  win_amount: number;
};

export const SpinRecordFields = [
  'game_id',
  'partner_code',
  'player_id',
  'round_type',
  'balance_after',
  'time',
  'balance_before',
  'bet_amount',
  'is_free_rounds',
  'is_free_spins',
  'round_id',
  'spin_id',
  'state',
  'win_amount',
];

export type GameDistributionMulRecord = {
  game_id: string;
  round_type: string;
  mul: number;
  count: number;
  RTP: number;
  time: Date;
};
