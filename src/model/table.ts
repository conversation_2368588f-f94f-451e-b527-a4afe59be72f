export type TableRecord = {
  time: string; // should parse to ISO string
  game_id: string;
  partner_code: string;
  player_id: string;
  round_type: string;
  balance_before: number;
  balance_after: number;
  round_id: string;
  event_id: string;
  state: string;
  bet_amount: number;
  win_amount: number;
};

export const TableRecordFields = [
  'time',
  'game_id',
  'partner_code',
  'player_id',
  'round_type',
  'balance_after',
  'balance_before',
  'round_id',
  'event_id',
  'state',
  'bet_amount',
  'win_amount',
];
