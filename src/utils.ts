import dayjs, { Dayjs } from 'dayjs';
import { ReturnProcessTick } from './repository/timeTickRepository';
import { STATISTIC_DIRECTION } from './handlers';
import utc from 'dayjs/plugin/utc';
import { WorkerName } from './config';
import { ClickhouseTableName } from './db';
type TimeAccuracy = 'minute' | 'hour' | 'day' | 'month' | 'year';

dayjs.extend(utc);

export const utcFormat = (time: Date | Dayjs, startAt: TimeAccuracy) => dayjs(time).utc().startOf(startAt).format();

export const isInconsistentTimeTick = (timeTick: ReturnProcessTick, direction: STATISTIC_DIRECTION) => {
  if (direction === 'next') {
    // If either tick is null, consider them inconsistent
    if (!timeTick.processedTickNext || !timeTick.processingTickNext) {
      return timeTick.processedTickNext !== timeTick.processingTickNext;
    }
    return (
      dayjs(timeTick.processedTickNext).utc().format('YYYY-MM-DDTHH:mm:ssZ') !==
      dayjs(timeTick.processingTickNext).utc().format('YYYY-MM-DDTHH:mm:ssZ')
    );
  }
  if (direction === 'backward') {
    // If either tick is null, consider them inconsistent
    if (!timeTick.processedTickBackward || !timeTick.processingTickBackward) {
      return timeTick.processedTickBackward !== timeTick.processingTickBackward;
    }
    return (
      dayjs(timeTick.processedTickBackward).utc().format('YYYY-MM-DDTHH:mm:ssZ') !==
      dayjs(timeTick.processingTickBackward).utc().format('YYYY-MM-DDTHH:mm:ssZ')
    );
  }
  return false;
};

export const getProcessingAndProcessedTick = (timeTick: ReturnProcessTick, direction: STATISTIC_DIRECTION) => {
  if (direction === 'next') return [timeTick.processingTickNext, timeTick.processedTickNext];
  if (direction === 'backward') return [timeTick.processingTickBackward, timeTick.processedTickBackward];
  return [];
};

export const getStartAndStopTime = (processingTick: Date, processedTick: Date, direction: STATISTIC_DIRECTION) => {
  if (direction === 'backward') return [processingTick, processedTick];
  if (direction === 'next') return [processedTick, processingTick];
  return [];
};

export const getDestinationTableNameByWorkerName = (workerName: WorkerName) => {
  switch (workerName) {
    case WorkerName.SpinHistoryWorker:
      return ClickhouseTableName.spin_history;
    case WorkerName.WalletHistoryWorker:
      return ClickhouseTableName.wallet_history;
    case WorkerName.TableHistoryWorker:
      return ClickhouseTableName.table_history;
    case WorkerName.TableWalletHistoryWorker:
      return ClickhouseTableName.wallet_history;
    case WorkerName.GameDistributionWorker:
      return ClickhouseTableName.game_distribution_mul;
    default:
      return null;
  }
};
export const toDateTimeString = (time: Date | Dayjs) => dayjs(time).utc().format('YYYY-MM-DDTHH:mm:ssZ');

export const regxTaskName = /(.+)-(next|backward)/;
