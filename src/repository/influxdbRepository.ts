import {
  FluxTableMetaData,
  InfluxDB,
  QueryApi,
  flux,
  fluxDuration,
  fluxDateTime,
  Point,
} from '@influxdata/influxdb-client';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import logger from '../logger';
import { Config, createConfig } from '../config';

dayjs.extend(utc);

export type TimePeriod = `${number}m` | `${number}h` | `${number}d` | `${number}w` | `${number}mo` | `${number}y`;

interface TimeTick {
  _measurement: string;
  _time: string;
}

export interface SumUpResult {
  result: string;
  table: number;
  _measurement: string;
  _time: string;
  _field: string;
  _value: number;
  _start: string;
  _stop: string;
  game_id: string;
  partner_code: string;
  player_id: string;
  round_type: string;
}

export type SumUpPayload = {
  bucket?: string;
  fromMeasurement: string;
  startTime: string;
  stopTime: string;
  timePeriod: TimePeriod;
  timezoneOffset?: number;
};

export type CountUpPayload = SumUpPayload & {
  countField?: string;
};

export type RawDataPayload<T> = Pick<SumUpPayload, 'bucket' | 'fromMeasurement' | 'startTime' | 'stopTime'> & {
  callback: (rows: T[]) => Promise<void>;
};

export interface CountResult {
  _start: string;
  _stop: string;
  _measurement: string;
  _field: string;
  _value: number;
  game_id: string;
  partner_code: string;
  player_id: string;
}

export type InfluxResult<T> = {
  results: T[];
  points: Point[];
};

export class InfluxDbRepository {
  private readonly sourceQueryApi: QueryApi;
  private readonly config: Config;

  constructor(
    private sourceRawDataDB: InfluxDB,
    private sourceOrg: string,
    private sourceBucket: string,
    private defaultMeasurement: string,
  ) {
    this.config = createConfig();
    this.sourceQueryApi = this.sourceRawDataDB.getQueryApi(this.sourceOrg);
  }

  public async getFirstTimeTick(fromMeasurement: string = this.defaultMeasurement) {
    const fluxQuery = flux`
      from(bucket: "${this.sourceBucket}")
        |> range(start: 2023-01-01T00:00:00Z)
        |> filter(fn: (r) => r._measurement == "${fromMeasurement}")
        |> keep(columns: ["_time"])
        |> sort(columns: ["_time"], desc: false)
        |> limit(n: 1)
    `;

    logger.debug(`${process.pid} 👉 ~ InfluxDbRepository ~ getFirstTimeTick ~ fluxQuery: ${fluxQuery}`);

    const fluxTables: TimeTick[] = await this.sourceQueryApi.collectRows(fluxQuery);

    return new Date(fluxTables[0]?._time || new Date());
  }

  public async sumUp({
    bucket = this.sourceBucket,
    fromMeasurement = this.defaultMeasurement,
    startTime,
    stopTime,
    timePeriod = '1m',
    timezoneOffset = 0,
  }: SumUpPayload): Promise<InfluxResult<SumUpResult>> {
    const timerStart = new Date().getTime();

    const fluxQuery = `
      import "date"
      import "timezone"
      option location = timezone.fixed(offset: ${timezoneOffset}h)

      from(bucket: "${bucket}")
      |> range(start: ${fluxDateTime(startTime)}, stop: ${fluxDateTime(stopTime)})
      |> filter(fn: (r) => r._measurement == "${fromMeasurement}" and (r._field == "WinAmount" or r._field == "BetAmount"))
      |> group(columns: ["game_id", "partner_code", "player_id", "round_type", "_field"])
      |> aggregateWindow(every: ${fluxDuration(timePeriod)}, fn: sum, createEmpty: false)
      |> map(fn: (r) => ({ r with _time: date.truncate(t: date.sub(d: ${fluxDuration(
        timePeriod,
      )}, from: r._time), unit: ${fluxDuration(timePeriod)})}))
    `;

    logger.debug(`${process.pid} 👉 ~ InfluxDbRepository ~ sumUp ~ fluxQuery: ${fluxQuery}`);

    return new Promise((resolve, reject) => {
      const results: SumUpResult[] = []; // 用于存储查询结果
      const queryApi = this.getQueryApi();

      queryApi.queryRows(fluxQuery, {
        next: (row: string[], tableMeta: FluxTableMetaData): boolean | void => {
          const o = tableMeta.toObject(row) as SumUpResult;
          results.push(o);
          this.config.debug && console.debug(JSON.stringify(o));
        },
        error: (error) => {
          logger.error(error);
          reject(error);
        },
        complete: async () => {
          const timerEnd = new Date().getTime();
          logger.info(
            `${process.pid} 👉 Influxdb aggregate (${startTime} ~ ${stopTime} in ${timePeriod}) (sumUpResults: ${
              results.length
            }; COST: ${timerEnd - timerStart}ms) SUCCESS`,
          );
          resolve({ results, points: [] });
        },
      });
    });
  }

  public async countUp({
    bucket = this.sourceBucket,
    fromMeasurement = this.defaultMeasurement,
    startTime,
    stopTime,
    timePeriod = '1m',
    countField = 'Count',
    timezoneOffset = 0,
  }: CountUpPayload): Promise<InfluxResult<SumUpResult>> {
    const timerStart = new Date().getTime();

    // we assume that countField is either 'Count' or other field name, if it is 'Count', we use sum function, otherwise we use count function.
    const aggregateMethod = countField === 'Count' ? 'sum' : 'count';

    const fluxQuery = `
      import "date"
      import "timezone"
      option location = timezone.fixed(offset: ${timezoneOffset}h)

      from(bucket: "${bucket}")
      |> range(start: ${fluxDateTime(startTime)}, stop: ${fluxDateTime(stopTime)})
      |> filter(fn: (r) => r._measurement == "${fromMeasurement}" and (r._field == "${countField}"))
      |> aggregateWindow(every: ${fluxDuration(timePeriod)}, fn: ${aggregateMethod}, createEmpty: false)
      |> map(fn: (r) => ({ r with _time: date.truncate(t: date.sub(d: ${fluxDuration(
        timePeriod,
      )}, from: r._time), unit: ${fluxDuration(timePeriod)}), _field: "Count" }))
    `;

    logger.debug(`${process.pid} 👉 ~ InfluxDbRepository ~ countUp ~ fluxQuery: ${fluxQuery}`);

    return new Promise((resolve, reject) => {
      const results: SumUpResult[] = []; // 用于存储查询结果
      const queryApi = this.getQueryApi();

      queryApi.queryRows(fluxQuery, {
        next: (row: string[], tableMeta: FluxTableMetaData): boolean | void => {
          const o = { ...tableMeta.toObject(row), _field: 'Count' } as SumUpResult;
          results.push(o);
          this.config.debug && console.debug(JSON.stringify(o));
        },
        error: (error) => {
          logger.error(error);
          reject(error);
        },
        complete: async () => {
          const timerEnd = new Date().getTime();

          logger.info(
            `${process.pid} 👉 Influxdb aggregate (${startTime} ~ ${stopTime} in ${timePeriod}) (countUpResults: ${
              results.length
            }; COST: ${timerEnd - timerStart}ms) SUCCESS`,
          );
          resolve({ results, points: [] });
        },
      });
    });
  }

  /**
   * getRawData accept two mode to handle the result:
   * 1. if `callback` function is provided, the result will be handled by the `callback` function.
   * 2. if `callback` function is not provided, the result will be stored in `results` array.
   * @param param.bucket source bucket
   * @param param.fromMeasurement source measurement
   * @param param.startTime start time
   * @param param.stopTime stop time
   * @param param.callback callback function
   * @returns InfluxResult<T>
   */
  public async getRawData<T>({
    bucket = this.sourceBucket,
    fromMeasurement = this.defaultMeasurement,
    startTime,
    stopTime,
    callback,
  }: RawDataPayload<T>): Promise<InfluxResult<T>> {
    const timerStart = new Date().getTime();

    const fluxQuery = `
      from(bucket: "${bucket}")
      |> range(start: ${fluxDateTime(startTime)}, stop: ${fluxDateTime(stopTime)})
      |> filter(fn: (r) => r._measurement == "${fromMeasurement}" and r.action != "Query")
      |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
    `;

    logger.debug(`${process.pid} 👉 ~ InfluxDbRepository ~ getRawData ~ fluxQuery: ${fluxQuery}`);

    return new Promise((resolve, reject) => {
      let resultCount = 0;
      let buffer: T[] = [];
      const bufferSize = 10000;

      const queryApi = this.getQueryApi();

      queryApi.queryRows(fluxQuery, {
        next: (row: string[], tableMeta: FluxTableMetaData): boolean | void => {
          const o = tableMeta.toObject(row) as T;
          this.config.debug && console.debug(JSON.stringify(o));

          buffer.push(o);

          // if buffer is full, we will return false to stop the query for `useResume` function handle the buffer write.
          return buffer.length < bufferSize;
        },
        error: (error) => {
          logger.error(error);
          reject(error);
        },
        complete: () => {
          const timerEnd = new Date().getTime();

          if (buffer.length === 0) return resolve({ results: [], points: [] });

          // if buffer is not empty, we will copy the buffer
          callback(buffer).then(() => {
            resultCount = resultCount + buffer.length;
            buffer = [];
            logger.info(
              `${
                process.pid
              } 👉 Influxdb getRawData (${startTime} ~ ${stopTime} (rawDataResults: ${resultCount}; COST: ${
                timerEnd - timerStart
              }ms) SUCCESS`,
            );
            resolve({ results: [], points: [] });
          });
        },
        // will be called when the next function return `false`
        useResume(resume: () => void) {
          callback(buffer).then(() => {
            resultCount = resultCount + buffer.length;
            buffer = [];
            // resume the query
            resume();
          });
        },
      });
    });
  }

  private getQueryApi() {
    return this.sourceQueryApi;
  }
}
