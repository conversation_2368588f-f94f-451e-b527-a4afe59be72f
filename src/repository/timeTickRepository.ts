import { K<PERSON> } from 'knex';
import { STATISTIC_DIRECTION } from '../handlers/jobHandler';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { WorkerName, createConfig, getQueueConfig } from '../config';

dayjs.extend(utc);
dayjs.extend(timezone);

export interface TimeTickPayload {
  name: string;
  first_record_tick?: Date;
  system_start_tick?: Date;
  processing_tick_backward?: Date;
  processed_tick_backward?: Date;
  processing_tick_next?: Date;
  processed_tick_next?: Date;
}

export interface TimeTick {
  id: number;
  name: string;
  first_record_tick: Date;
  system_start_tick: Date;
  processing_tick_backward: Date;
  processed_tick_backward: Date;
  processing_tick_next: Date;
  processed_tick_next: Date;
}

export interface ReturnProcessTick {
  processTimeTick: Date;
  firstRecordTick: Date;
  systemStartTick: Date;
  processedTickBackward: Date;
  processedTickNext: Date;
  processingTickBackward: Date;
  processingTickNext: Date;
}

export const FIRST_TIME_TICK = 'first_time_tick';
export interface TimeTickRepositoryInterface {
  checkTableIsNewCreated(): Promise<boolean>;
  insertFirstTimeTick(timeTick: Date): Promise<boolean>;
  insertTimeTick(payload: TimeTickPayload): Promise<boolean>;
  dropTimeTickTable(): Promise<void>;
  findOneByName(name: string, options?: { fromMaster: boolean }): Promise<TimeTickPayload>;
  getProcessTimeTick(tickName: string, direction: STATISTIC_DIRECTION): Promise<ReturnProcessTick>;
  updateProcessingTimeTick(
    taskName: string,
    timeTick: Date,
    direction: STATISTIC_DIRECTION,
    trx?: Knex.Transaction,
  ): Promise<number>;
  updateProcessedTimeTick(
    taskName: string,
    timeTick: Date,
    direction: STATISTIC_DIRECTION,
    trx?: Knex.Transaction,
  ): Promise<number>;
  processToSuitTimeTick(tickName: string, timeTick: Date): Date;
}

export class TimeTickRepository implements TimeTickRepositoryInterface {
  private tableName = 'time_tick';
  private config = createConfig();
  private dbMaster: Knex;

  constructor({ dbMaster }: { dbMaster: Knex }) {
    this.dbMaster = dbMaster;
  }

  public async checkTableIsNewCreated() {
    const isTableExist = await this.dbMaster.schema.hasTable(this.tableName);

    if (!isTableExist) throw new Error(`❌ The table [${this.tableName}] is not exist, Exit!! ❌`);

    // check if table is empty
    const isEmpty = (await this.dbMaster(this.tableName).select().limit(1)).length === 0;

    return isEmpty;
  }

  public async insertFirstTimeTick(timeTick: Date) {
    return await this.insertTimeTick({
      name: FIRST_TIME_TICK,
      first_record_tick: timeTick,
      system_start_tick: new Date(),
      processing_tick_backward: timeTick,
      processed_tick_backward: timeTick,
      processing_tick_next: new Date(),
      processed_tick_next: new Date(),
    });
  }

  public async insertTimeTick(payload: TimeTickPayload) {
    const result = await this.dbMaster(this.tableName).returning(['id']).insert(payload).onConflict('name').ignore(); // using ignore to prevent overwriting existing records

    return result && result.length > 0;
  }

  public async updateProcessingTimeTick(
    taskName: string,
    timeTick: Date,
    direction: STATISTIC_DIRECTION,
    trx?: Knex.Transaction,
  ) {
    const updatePayload = {
      [direction === 'backward' ? 'processing_tick_backward' : 'processing_tick_next']: timeTick,
    };
    const builder = this.dbMaster(this.tableName).where({ name: taskName }).update(updatePayload);

    return await (trx ? builder.transacting(trx) : builder);
  }

  public async updateProcessedTimeTick(
    taskName: string,
    timeTick: Date,
    direction: STATISTIC_DIRECTION,
    trx?: Knex.Transaction,
  ) {
    const updatePayload = {
      [direction === 'backward' ? 'processed_tick_backward' : 'processed_tick_next']: timeTick,
    };
    const builder = this.dbMaster(this.tableName).where({ name: taskName }).update(updatePayload);

    return await (trx ? builder.transacting(trx) : builder);
  }

  public async dropTimeTickTable() {
    return await this.dbMaster.schema.dropTableIfExists(this.tableName);
  }

  public async findOneByName(name: string, options?: { fromMaster: boolean }) {
    if (options?.fromMaster) return await this.dbMaster(this.tableName).where({ name }).first();
    return await this.dbMaster(this.tableName).where({ name }).first();
  }

  public async getProcessTimeTick(tickName: string, direction: STATISTIC_DIRECTION) {
    const timeTick: TimeTick = await this.dbMaster(this.tableName).where({ name: tickName }).first();
    const queueConfig = getQueueConfig(tickName);

    let processTimeTick = new Date();
    if (direction === 'backward')
      processTimeTick = this.bothTimeTickAreNotSet(timeTick.processing_tick_backward, timeTick.processed_tick_backward)
        ? this.processToSuitTimeTick(queueConfig.workerName, timeTick.system_start_tick)
        : timeTick.processed_tick_backward;

    if (direction === 'next')
      processTimeTick = this.bothTimeTickAreNotSet(timeTick.processing_tick_next, timeTick.processed_tick_next)
        ? this.processToSuitTimeTick(queueConfig.workerName, timeTick.system_start_tick)
        : timeTick.processed_tick_next;

    return {
      processTimeTick,
      firstRecordTick: timeTick.first_record_tick,
      systemStartTick: timeTick.system_start_tick,
      processedTickBackward: timeTick.processed_tick_backward,
      processedTickNext: timeTick.processed_tick_next,
      processingTickBackward: timeTick.processing_tick_backward,
      processingTickNext: timeTick.processing_tick_next,
    };
  }

  public processToSuitTimeTick(workerName: WorkerName, timeTick: Date) {
    switch (workerName) {
      case WorkerName.SpinHistoryWorker:
      case WorkerName.WalletHistoryWorker:
      case WorkerName.TableHistoryWorker:
      case WorkerName.TableWalletHistoryWorker:
      case WorkerName.GameDistributionWorker:
        return dayjs(timeTick).subtract(1, 'minute').startOf('minute').toDate();
      default:
        return timeTick;
    }
  }

  private bothTimeTickAreNotSet(processing_tick_backward: Date, processed_tick_backward: Date) {
    return !processing_tick_backward && !processed_tick_backward;
  }
}
