import Redis from 'ioredis';
import { GranularitySumUpType } from './totalSumUpRepository';
import logger from '../logger';

export class RedisRepository {
  constructor(private redis: Redis) {}

  storeStatisticResult = async (key: string, results: GranularitySumUpType[], expire: number) => {
    const genStoreKey = (recordTime: Date, partnerCode: string, gameId: string, roundType: string) =>
      `${recordTime.toISOString()}|${partnerCode}|${gameId}|${roundType}`;
    const startTime = new Date();

    const zkey = `z-${key}`;
    const hkey = `h-${key}`;
    logger.info(
      `🚀 ~ PreStoreWorker ~ zkey:${zkey}; hkey=${hkey}; \nresultAmounts:${results.length}; ${
        results.length
          ? `results[0]:${JSON.stringify(results[0])};results[-1]:${JSON.stringify(results[results.length - 1])}`
          : ''
      }`,
    );

    // 如果数据量非常大，可以考虑将 results 分批处理
    const batchSize = 500; // 或根据实际情况调整

    for (let i = 0; i < results.length; i += batchSize) {
      const batchResults = results.slice(i, i + batchSize);

      // 使用事务
      const transaction = this.redis.multi();

      transaction.zadd(
        zkey,
        ...batchResults
          .map((result) => [
            new Date(result.record_time!).getTime(),
            genStoreKey(result.record_time!, result.partner_code || '', result.game_id || '', result.round_type || ''),
          ])
          .flat(),
      );

      transaction.hset(
        hkey,
        ...batchResults
          .map((result) => [
            genStoreKey(result.record_time!, result.partner_code || '', result.game_id || '', result.round_type || ''),
            JSON.stringify(result),
          ])
          .flat(),
      );

      transaction.expire(zkey, expire);
      transaction.expire(hkey, expire);

      // 执行事务并处理错误
      try {
        await transaction.exec();
      } catch (error) {
        console.error('Redis transaction error:', error);
        logger.error(`Redis transaction error: ${error}`);
        throw error;
      }
    }

    const endTime = new Date();
    logger.info(`🚀 ~ PreStoreWorker ~ (${key}) ~ COST: ${endTime.getTime() - startTime.getTime()}ms `);
  };

  recordInconsistentTimeTickTimes = async (jobName: string) => {
    const inconsistentTimeTickTimesKey = `inconsistentTimeTickTimes-${jobName}`;
    return await this.redis.incr(inconsistentTimeTickTimesKey);
  };

  resetInConsistentTimeTickTimes = async (jobName: string) => {
    const inconsistentTimeTickTimesKey = `inconsistentTimeTickTimes-${jobName}`;
    return await this.redis.del(inconsistentTimeTickTimesKey);
  };
}
