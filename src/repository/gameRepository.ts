import { Knex } from 'knex';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { createConfig } from '../config';

dayjs.extend(utc);
dayjs.extend(timezone);

export interface Game {
  id: string;
  name: string;
}

export interface GameRepositoryInterface {
  checkTableIsNewCreated(): Promise<boolean>;
  getAllGames(): Promise<Game[]>;
  getGameById(id: string): Promise<Game | undefined>;
  getGameByName(name: string): Promise<Game | undefined>;
}

export class GameRepository implements GameRepositoryInterface {
  private tableName = 'games';
  private config = createConfig();
  private dbMaster: Knex;

  constructor({ dbMaster }: { dbMaster: Knex }) {
    this.dbMaster = dbMaster;
  }

  public async checkTableIsNewCreated() {
    const isTableExist = await this.dbMaster.schema.hasTable(this.tableName);

    if (!isTableExist) throw new Error(`❌ The table [${this.tableName}] is not exist, Exit!! ❌`);

    // check if table is empty
    const isEmpty = (await this.dbMaster(this.tableName).select().limit(1)).length === 0;

    return isEmpty;
  }

  public async getAllGames() {
    return await this.dbMaster(this.tableName).select();
  }

  public async getGameById(id: string) {
    return await this.dbMaster(this.tableName).where({ id }).first();
  }

  public async getGameByName(name: string) {
    return await this.dbMaster(this.tableName).where({ name }).first();
  }
}
