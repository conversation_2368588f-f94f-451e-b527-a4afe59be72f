import { ClickHouseClient } from '@clickhouse/client';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import logger from '../logger';
import { Config } from '../config';
import { InfluxdbSpinRecordType } from '../worker/spinHistoryWorker';
import { mapKeys, pick, snakeCase } from 'lodash';
import { SpinRecord, GameDistributionMulRecord } from '../model/spin';
import { InfluxdbTableRecordType } from '../worker/tableHistoryWorker';
import { TableRecord } from '../model/table';

dayjs.extend(utc);

export class ClickhouseRepository {
  private readonly maxRetries = 3;
  private spinHistoryTableName: string = 'spin_history';
  private tableHistoryTableName: string = 'table_history';
  private tableGameDistributionMul: string = 'game_distribution_mul';
  private config: Config;
  private client: ClickHouseClient;

  constructor({
    config,
    client,
    spinHistoryTableName,
    tableHistoryTableName,
  }: {
    config: Config;
    client: ClickHouseClient;
    spinHistoryTableName?: string;
    tableHistoryTableName?: string;
  }) {
    this.config = config;
    this.client = client;

    if (spinHistoryTableName) {
      this.spinHistoryTableName = spinHistoryTableName;
    }
    if (tableHistoryTableName) {
      this.tableHistoryTableName = tableHistoryTableName;
    }
  }

  public async queryGameDistributionMul(time: Date) {
    const startTime = dayjs(time).utc().format('YYYY-MM-DD HH:mm:ss.000');
    const endTime = dayjs(time).add(1, 'day').utc().format('YYYY-MM-DD HH:mm:ss.000');
    return this.client.query({
      query: `
        WITH RTPs AS (
          SELECT
            game_id,
            round_type,
            toFloat64(sum(win_amount)) / toFloat64(sum(bet_amount)) AS RTP,
            multiIf(
              sum(bet_amount) < 0, -1,
              RTP == 0, 0,
              RTP <= 0.5, 0.5,
              RTP <= 1, 1,
              RTP <= 2, 2,
              RTP <= 3, 3,
              RTP <= 4, 4,
              RTP <= 5, 5,
              RTP <= 10, 10,
              RTP <= 30, 30,
              RTP <= 50, 50,
              RTP <= 80, 80,
              RTP <= 100, 100,
              RTP <= 300, 300,
              RTP <= 600, 600,
              -2) AS mul
          FROM spin_history
          WHERE (
            time >= toDateTime64('${startTime}', 3)
            AND time < toDateTime64('${endTime}', 3)
          )
          GROUP BY game_id, round_type, round_id
        )
        SELECT game_id, round_type, toString(mul) AS mul, toFloat64(count(mul)) AS count FROM RTPs GROUP BY game_id, round_type, mul
  `,
    });
  }

  public async insertGameDistributionMul(records: GameDistributionMulRecord[]) {
    return this.client.insert({
      table: this.tableGameDistributionMul,
      values: records,
      format: 'JSONEachRow',
    });
  }

  public async insertSpinRecords(records: InfluxdbSpinRecordType[]): Promise<void> {
    if (records.length === 0) return;

    const clickHouseRows = records.map(this.toClickHouseRows);

    await this.retryInsert(clickHouseRows, this.maxRetries);
    logger.debug('Records spin inserted successfully');
  }

  public async insertTableGameRecords(records: InfluxdbTableRecordType[]): Promise<void> {
    if (records.length === 0) return;

    const clickHouseRows = records.map(this.toTableGameClickHouseRows);

    await this.retryInsertTableGameRecords(clickHouseRows, this.maxRetries);
    logger.debug('Records table game inserted successfully');
  }

  private toClickHouseRows(record: InfluxdbSpinRecordType): SpinRecord {
    const pickFields = [
      'game_id',
      'partner_code',
      'player_id',
      'round_type',
      'BalanceAfter',
      'BalanceBefore',
      'BetAmount',
      'IsFreeRounds',
      'IsFreeSpins',
      'RoundID',
      'SpinID',
      'State',
      'WinAmount',
    ];

    return {
      ...(mapKeys(pick(record, pickFields), (_v, k) => snakeCase(k)) as SpinRecord),
      time: dayjs(record._time).utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
    };
  }

  private toTableGameClickHouseRows(record: InfluxdbTableRecordType): TableRecord {
    const pickFields = [
      'game_id',
      'partner_code',
      'player_id',
      'round_type',
      'BalanceAfter',
      'BalanceBefore',
      'BetAmount',
      'WinAmount',
      'RoundID',
      'EventID',
      'State',
    ];

    const baseRecord = mapKeys(pick(record, pickFields), (_v, k) => snakeCase(k)) as TableRecord;

    let state = baseRecord.state;
    try {
      if (typeof state === 'string') {
        const parsedState = JSON.parse(state);
        state = JSON.stringify(parsedState);
      }
    } catch (error: unknown) {
      logger.warn('Failed to parse state JSON:', {
        originalState: state,
        error: error instanceof Error ? error.message : String(error),
        record: {
          game_id: baseRecord.game_id,
          round_id: baseRecord.round_id,
          player_id: baseRecord.player_id,
        },
      });
      state = state.replace(/\\/g, '\\\\').replace(/"/g, '\\"');
    }

    const finalRecord = {
      ...baseRecord,
      state,
      time: dayjs(record._time).utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
    };

    try {
      JSON.stringify(finalRecord);
    } catch (error: unknown) {
      logger.error('Failed to serialize record:', {
        error: error instanceof Error ? error.message : String(error),
        record: {
          game_id: finalRecord.game_id,
          round_id: finalRecord.round_id,
          player_id: finalRecord.player_id,
          state: finalRecord.state,
        },
      });
      throw error;
    }

    return finalRecord;
  }

  private async retryInsert(records: (SpinRecord | TableRecord)[], retries: number = 3): Promise<void> {
    for (let attempt = 0; attempt < retries; attempt++) {
      try {
        await this.client.insert({
          table: this.spinHistoryTableName,
          values: records,
          format: 'JSONEachRow',
        });
        return;
      } catch (error) {
        logger.warn(`❗️ retryInsert: attempt ${attempt + 1} failed, retrying...`, error);

        if (attempt + 1 === retries) {
          logger.error('❌ Max retries reached, failing insert');
          throw new Error('Max retries reached, failing insert');
        }
      }
    }
  }

  private async retryInsertTableGameRecords(records: TableRecord[], retries: number = 3): Promise<void> {
    for (let attempt = 0; attempt < retries; attempt++) {
      try {
        await this.client.insert({
          table: this.tableHistoryTableName,
          values: records,
          format: 'JSONEachRow',
        });
        return;
      } catch (error) {
        logger.warn(`❗️ retryInsertTableGameRecords: attempt ${attempt + 1} failed, retrying...`, error);

        if (attempt + 1 === retries) {
          logger.error('❌ Max retries reached, failing insert table game records');
          throw new Error('Max retries reached, failing insert table game records');
        }
      }
    }
  }
}
