import { K<PERSON> } from 'knex';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { createConfig } from '../config';
import logger from '../logger';
import { InfluxWalletType } from '../worker/walletHistoryWorker';

dayjs.extend(utc);
dayjs.extend(timezone);

export const WALLET_DATA_SCHEMA = {
  PARTNER_CODE: 'partner_code',
  PLAYER_ID: 'player_id',
  GAME_ID: 'game_id',
  ACTION: 'action',
  RECORD_TIME: 'record_time',
  AMOUNT: 'amount',
  BALANCE: 'balance',
  PARTNER_TRANSACTION_ID: 'partner_transaction_id',
  TRANSACTION_ID: 'transaction_id',
};

export type WalletDataPayload = {
  partner_code: string;
  player_id: string;
  action: string;
  record_time: Date;
  amount: number;
  balance: number;
  partner_transaction_id: string;
  transaction_id: string;
};

export interface WalletDataRepositoryInterface {
  checkTableIsNewCreated(): Promise<boolean>;
  insertWalletData(records: InfluxWalletType[], trx?: Knex.Transaction): Promise<number[]>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  transaction(): Promise<Knex.Transaction<any, any[]>>;
}

export class WalletDataRepository implements WalletDataRepositoryInterface {
  private tableName = 'wallet_history';
  private config = createConfig();
  private dbMaster: Knex;

  constructor({ dbMaster }: { dbMaster: Knex }) {
    this.dbMaster = dbMaster;
  }

  public async checkTableIsNewCreated() {
    const isTableExist = await this.dbMaster.schema.hasTable(this.tableName);

    if (!isTableExist) throw new Error(`❌ The table [${this.tableName}] is not exist, Exit!! ❌`);

    // check if table is empty
    const isEmpty = (await this.dbMaster(this.tableName).select().limit(1)).length === 0;

    return isEmpty;
  }

  public async insertWalletData(records: InfluxWalletType[], trx?: Knex.Transaction) {
    const batchSize = 1000; // 每次批量插入的大小
    const maxRetries = 3; // 最大重試次數
    const results: number[] = [];
    const identifiers: { _time: string; TransactionID: string }[] = records.map((record) => ({
      _time: record._time,
      TransactionID: record.TransactionID,
    }));

    if (records.length === 0) return results;

    // Helper function to insert a single batch with retry mechanism
    const insertBatchWithRetry = async (batch: InfluxWalletType[], retries: number = 0): Promise<void> => {
      try {
        const builder = this.dbMaster(this.tableName).insert(this.constructPayload(batch));
        await (trx ? builder.transacting(trx) : builder);
      } catch (error) {
        if (retries < maxRetries) {
          logger.warn(`⚠️ Retry insertBatchWithRetry: attempt ${retries + 1} failed, retrying...`);
          await insertBatchWithRetry(batch, retries + 1);
        } else {
          logger.error(`❌ insertBatchWithRetry ~ error: ${error}`);
          throw error;
        }
      }
    };

    try {
      for (let i = 0; i < records.length; i += batchSize) {
        const batch = records.slice(i, i + batchSize);
        await insertBatchWithRetry(batch);
        results.push(...batch.map(() => 1)); // Assuming success returns 1 for each record
      }
      return results;
    } catch (error) {
      logger.error(`❌ insertWalletData ~ error: ${error}`);
      // Implement rollback logic since transaction is not being used
      // Rollback mechanism could involve deleting inserted data if possible

      if (!trx) {
        // This assumes you have a way to identify and remove partially inserted data
        // For example, using a unique identifier or timestamp
        await this.rollbackInsertedData(identifiers);
      } else {
        await trx.rollback();
      }
      throw error;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public async transaction(): Promise<Knex.Transaction<any, any[]>> {
    return await this.dbMaster.transaction();
  }

  // Rollback function to delete inserted data
  private async rollbackInsertedData(identifiers: { _time: string; TransactionID: string }[]) {
    try {
      const deletePromises = identifiers.map(async ({ _time, TransactionID }) => {
        await this.dbMaster(this.tableName).where('_time', _time).andWhere('TransactionID', TransactionID).del();
      });
      await Promise.all(deletePromises);
      logger.info('✅ Rollback successful: Inserted data has been deleted.');
    } catch (rollbackError) {
      logger.error(`❌ Rollback failed: ${rollbackError}`);
      throw rollbackError;
    }
  }

  private constructPayload(records: InfluxWalletType[]) {
    return records.map((e) => ({
      partner_code: e.partner_code,
      player_id: e.player_id,
      action: e.action,
      record_time: new Date(e._time),
      amount: e.Amount,
      balance: e.Balance,
      partner_transaction_id: e.PartnerTransactionID,
      transaction_id: e.TransactionID,
    }));
  }
}
