import { <PERSON><PERSON> } from 'knex';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { createConfig } from '../config';
import { SumUpResult } from './influxdbRepository';
import logger from '../logger';
import { ClickhouseTableName } from '../db';

dayjs.extend(utc);
dayjs.extend(timezone);

export const SUM_UP_SCHEMA = {
  PARTNER_CODE: 'partner_code',
  PLAYER_ID: 'player_id',
  GAME_ID: 'game_id',
  ROUND_TYPE: 'round_type',
  RECORD_TIME: 'record_time',
  BET_AMOUNT: 'bet_amount',
  WIN_AMOUNT: 'win_amount',
  SPIN_COUNT: 'spin_count',
  GRANULARITY: 'granularity',
};

export const SUM_UP_NO_PLAYER_SCHEMA = {
  PARTNER_CODE: 'partner_code',
  GAME_ID: 'game_id',
  ROUND_TYPE: 'round_type',
  PLAYER_COUNT: 'player_count',
  RECORD_TIME: 'record_time',
  BET_AMOUNT: 'bet_amount',
  WIN_AMOUNT: 'win_amount',
  SPIN_COUNT: 'spin_count',
  GRANULARITY: 'granularity',
};

type UpsertPayload = {
  partner_code: string;
  player_id: string;
  game_id?: string;
  round_type?: string;
  record_time?: Date;
  bet_amount: number;
  win_amount: number;
  spin_count: number;
  granularity: Granularity;
};

export enum Granularity {
  min = 'min',
  hourly = 'hourly',
  daily = 'daily',
  weekly = 'weekly',
  monthly = 'monthly',
  yearly = 'yearly',
  all = 'all',
  totals = 'totals',
}

const GranularityToPGTrunc: { [x in Granularity]: string } = {
  [Granularity.min]: 'minute',
  [Granularity.hourly]: 'hour',
  [Granularity.daily]: 'day',
  [Granularity.weekly]: 'week',
  [Granularity.monthly]: 'month',
  [Granularity.yearly]: 'year',
  [Granularity.all]: 'minute',
  [Granularity.totals]: 'minute',
};

export type TotalSumUp = {
  partner_code: string;
  player_id: string;
  bet_amount: number;
  win_amount: number;
  spin_count: number;
  granularity: Granularity;
};

export type GranularitySumUp = TotalSumUp & {
  game_id: string;
  round_type: string;
  record_time: Date;
};

export type AggregateSumUpPayload = {
  startTime: string;
  stopTime: string;
  fromGranularity: Granularity;
  toGranularity: Granularity;
  timezoneOffset?: number;
};

export type GranularitySumUpNoPlayer = Omit<GranularitySumUp, 'player_id'>;
export type GranularitySumUpTotalPartner = Omit<TotalSumUp, 'player_id'> & Partial<GranularitySumUp>;
export type GranularitySumUpTotalGame = Omit<TotalSumUp, 'partner_code' | 'player_id'> &
  Partial<GranularitySumUp> & {
    game_id: string;
  };
export type GranularitySumUpType = GranularitySumUpNoPlayer | GranularitySumUpTotalPartner | GranularitySumUpTotalGame;

export interface TotalSumUpRepositoryInterface {
  checkTableIsNewCreated(): Promise<boolean>;
  upsert(
    influxdbSumUpRecord: SumUpResult[] | GranularitySumUp[],
    granularity: Granularity,
    trx?: Knex.Transaction,
  ): Promise<number[][]>;
  aggregateSumUpByGranularity(
    { startTime, stopTime, fromGranularity, toGranularity, timezoneOffset }: AggregateSumUpPayload,
    trx?: Knex.Transaction,
  ): Promise<GranularitySumUp[]>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  transaction(): Promise<Knex.Transaction<any, any[]>>;
  getSumUpByGranularity(granularity: Granularity, startDate: Date, endDate: Date): Promise<GranularitySumUpNoPlayer[]>;
  getSumUpTotalGranularityBy(
    tableName: ClickhouseTableName,
    startDate: Date,
    endDate: Date,
  ): Promise<GranularitySumUpType[]>;
  deleteDataInRange(granularity: Granularity, startDate: Date, endDate: Date, trx?: Knex.Transaction): Promise<number>;
  deleteDataInRangeByTableName(
    tableName: ClickhouseTableName,
    startDate: Date,
    endDate: Date,
    trx?: Knex.Transaction,
  ): Promise<number>;
  refreshMaterializedView(granularity: Granularity): Promise<void>;
  refreshMaterializedViewByTableNames(tableNames: ClickhouseTableName[]): Promise<void>;
}

export class TotalSumUpRepository implements TotalSumUpRepositoryInterface {
  private tableName = 'sum_up';
  private tableNameNoPlayer = 'sum_up_no_player';
  private config = createConfig();
  private dbMaster: Knex;

  constructor({ dbMaster }: { dbMaster: Knex }) {
    this.dbMaster = dbMaster;
  }

  public async checkTableIsNewCreated() {
    const isTableExist = await this.dbMaster.schema.hasTable(this.tableName);

    if (!isTableExist) throw new Error(`❌ The table [${this.tableName}] is not exist, Exit!! ❌`);

    // check if table is empty
    const isEmpty = (await this.dbMaster(this.tableName).select().limit(1)).length === 0;

    return isEmpty;
  }

  public async aggregateSumUpByGranularity(
    { startTime, stopTime, fromGranularity, toGranularity, timezoneOffset = 0 }: AggregateSumUpPayload,
    trx?: Knex.Transaction,
  ) {
    const tableName = this.getTableName(fromGranularity);

    const recordTimeRaw = this.dbMaster.raw(
      `(DATE_TRUNC('${GranularityToPGTrunc[toGranularity]}', ${SUM_UP_SCHEMA.RECORD_TIME} AT TIME ZONE 'UTC' AT TIME ZONE '+${timezoneOffset}') AT TIME ZONE '+${timezoneOffset}')::timestamptz as ${SUM_UP_SCHEMA.RECORD_TIME}`,
    );
    const recordTimeGroupRaw = this.dbMaster.raw(
      `(DATE_TRUNC('${GranularityToPGTrunc[toGranularity]}', ${SUM_UP_SCHEMA.RECORD_TIME} AT TIME ZONE 'UTC' AT TIME ZONE '+${timezoneOffset}') AT TIME ZONE '+${timezoneOffset}')::timestamptz`,
    );

    const queryBuilder = this.dbMaster(tableName)
      .select([
        SUM_UP_SCHEMA.PARTNER_CODE,
        SUM_UP_SCHEMA.PLAYER_ID,
        SUM_UP_SCHEMA.GAME_ID,
        SUM_UP_SCHEMA.ROUND_TYPE,
        this.dbMaster.raw(`sum(${SUM_UP_SCHEMA.BET_AMOUNT}) as ${SUM_UP_SCHEMA.BET_AMOUNT}`),
        this.dbMaster.raw(`sum(${SUM_UP_SCHEMA.WIN_AMOUNT}) as ${SUM_UP_SCHEMA.WIN_AMOUNT}`),
        this.dbMaster.raw(`sum(${SUM_UP_SCHEMA.SPIN_COUNT}) as ${SUM_UP_SCHEMA.SPIN_COUNT}`),
        recordTimeRaw,
      ])
      .where(
        this.dbMaster.raw(`${SUM_UP_SCHEMA.RECORD_TIME} >= ? AND ${SUM_UP_SCHEMA.RECORD_TIME} < ?`, [
          startTime,
          stopTime,
        ]),
      )
      .groupByRaw(
        `${SUM_UP_SCHEMA.PARTNER_CODE},
        ${SUM_UP_SCHEMA.PLAYER_ID},
        ${SUM_UP_SCHEMA.GAME_ID},
        ${SUM_UP_SCHEMA.ROUND_TYPE},
        ?`,
        [recordTimeGroupRaw],
      ).orderByRaw(`
      ${SUM_UP_SCHEMA.RECORD_TIME},
      ${SUM_UP_SCHEMA.PARTNER_CODE},
      ${SUM_UP_SCHEMA.PLAYER_ID},
      ${SUM_UP_SCHEMA.GAME_ID},
      ${SUM_UP_SCHEMA.ROUND_TYPE}`);

    // this.config.debug &&
    logger.debug(`⚡️ query string --> ${queryBuilder.toString()}`);

    const results: GranularitySumUp[] = await (trx ? queryBuilder.transacting(trx) : queryBuilder);

    return results;
  }

  public async upsert(
    sumUpRecord: SumUpResult[] | GranularitySumUp[],
    granularity: Granularity,
    trx?: Knex.Transaction,
  ) {
    const tableName = this.getTableName(granularity);
    const upsertResult: number[][] = [];

    if (sumUpRecord.length === 0) return upsertResult;

    for (const record of sumUpRecord) {
      // assert record is SumUpResult or GranularitySumUp
      const payload = '_time' in record ? this.constructPayload(record, granularity) : { ...record, granularity };

      const conflictColumns = this.getConflictColumns(granularity);

      const result = await this.retryUpsert(tableName, payload, conflictColumns, trx);
      upsertResult.push(result);
    }

    return upsertResult;
  }

  public async deleteDataInRange(granularity: Granularity, startDate: Date, endDate: Date, trx?: Knex.Transaction) {
    const tableName = this.getTableName(granularity);
    const deleteBuilder = this.dbMaster(tableName)
      .where(SUM_UP_SCHEMA.RECORD_TIME, '>=', startDate)
      .andWhere(SUM_UP_SCHEMA.RECORD_TIME, '<', endDate)
      .delete();

    return await (trx ? deleteBuilder.transacting(trx) : deleteBuilder);
  }

  public async deleteDataInRangeByTableName(
    tableName: ClickhouseTableName,
    startDate: Date,
    endDate: Date,
    trx?: Knex.Transaction,
  ) {
    const deleteBuilder = this.dbMaster(tableName)
      .where(SUM_UP_SCHEMA.RECORD_TIME, '>=', startDate)
      .andWhere(SUM_UP_SCHEMA.RECORD_TIME, '<', endDate)
      .delete();

    return await (trx ? deleteBuilder.transacting(trx) : deleteBuilder);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public async transaction(): Promise<Knex.Transaction<any, any[]>> {
    return await this.dbMaster.transaction();
  }

  public async getSumUpByGranularity(granularity: Granularity, startDate: Date, endDate: Date) {
    const tableName = this.getTableNameNoPlayer(granularity);
    return await this.dbMaster(tableName).select().whereBetween('record_time', [startDate, endDate]);
  }

  public async getSumUpTotalGranularityBy(tableName: ClickhouseTableName, startDate: Date, endDate: Date) {
    return await this.dbMaster(tableName).select().whereBetween('record_time', [startDate, endDate]);
  }

  public async refreshMaterializedView(granularity: Granularity) {
    if (granularity === Granularity.totals) {
      return await this.dbMaster.raw(`REFRESH MATERIALIZED VIEW CONCURRENTLY totals`);
    }

    const tableName =
      granularity === Granularity.all ? this.getTableName(granularity) : this.getTableNameNoPlayer(granularity);
    return await this.dbMaster.raw(`REFRESH MATERIALIZED VIEW CONCURRENTLY ${tableName}`);
  }

  public async refreshMaterializedViewByTableNames(tableNames: ClickhouseTableName[]) {
    const query = tableNames.map((tableName) => `REFRESH MATERIALIZED VIEW CONCURRENTLY ${tableName};`).join(' ');
    return await this.dbMaster.raw(query);
  }

  private async retryUpsert(
    tableName: string,
    payload: UpsertPayload,
    conflictColumns: string[],
    trx?: Knex.Transaction,
    retries: number = 3,
  ): Promise<number[]> {
    try {
      const builder = this.dbMaster(tableName)
        .insert(payload)
        .onConflict(conflictColumns)
        .merge({
          bet_amount: this.dbMaster.raw('?? + ?', [`${tableName}.bet_amount`, payload.bet_amount]),
          win_amount: this.dbMaster.raw('?? + ?', [`${tableName}.win_amount`, payload.win_amount]),
          spin_count: this.dbMaster.raw('?? + ?', [`${tableName}.spin_count`, payload.spin_count]),
        });

      return await (trx ? builder.transacting(trx) : builder);
    } catch (error) {
      await trx?.rollback();
      if (retries > 0) {
        logger.error(`🚀 ~ TotalSumUpRepository ~ retryUpsert: (retry left time: ${retries}) error: ${error}`);
        if (this.isDeadlockError(error)) {
          logger.error(`Deadlock detected, retrying... ${retries} attempts left.`);

          await new Promise((resolve) => setTimeout(resolve, 100)); // 等待100ms重试
        }
        return await this.retryUpsert(tableName, payload, conflictColumns, trx, retries - 1);
      } else {
        throw error;
      }
    }
  }

  private isDeadlockError(error: unknown): boolean {
    // deadlock code: 40P01
    return (error as { code: string }).code === '40P01';
  }

  private getConflictColumns(granularity: Granularity) {
    return granularity === Granularity.all
      ? ['player_id', 'partner_code', 'granularity']
      : ['partner_code', 'game_id', 'player_id', 'round_type', 'record_time', 'granularity'];
  }

  private constructPayload(record: SumUpResult, granularity: Granularity) {
    return granularity === Granularity.all
      ? {
          player_id: record.player_id,
          partner_code: record.partner_code,
          bet_amount: record._field === 'BetAmount' ? record._value || 0 : 0,
          win_amount: record._field === 'WinAmount' ? record._value || 0 : 0,
          spin_count: record._field === 'Count' ? record._value || 1 : 0,
          granularity: granularity,
        }
      : {
          partner_code: record.partner_code,
          game_id: record.game_id,
          player_id: record.player_id,
          round_type: record.round_type,
          bet_amount: record._field === 'BetAmount' ? record._value || 0 : 0,
          win_amount: record._field === 'WinAmount' ? record._value || 0 : 0,
          spin_count: record._field === 'Count' ? record._value || 1 : 0,
          record_time: new Date(record._time),
          granularity: granularity,
        };
  }

  private getTableName(granularity: Granularity) {
    return `${this.tableName}_${granularity}`;
  }

  private getTableNameNoPlayer(granularity: Granularity) {
    return `${this.tableNameNoPlayer}_${granularity}`;
  }
}
