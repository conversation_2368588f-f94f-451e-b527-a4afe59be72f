import { ClickHouseClient } from '@clickhouse/client';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { InfluxWalletType } from '../worker/walletHistoryWorker';
import logger from '../logger';

dayjs.extend(utc);

const maxRetryAttempts = 3;

type WalletHistoryClickHouseRow = {
  partner_code: string;
  player_id: string;
  partner_transaction_id: string;
  transaction_id: string;
  record_time: string;
  action: string;
  amount: number;
  balance: number;
  event_time: string;
};

export class WalletHistoryRepository {
  private clickHouseClient: ClickHouseClient;

  constructor(clickHouseClient: ClickHouseClient) {
    this.clickHouseClient = clickHouseClient;
  }

  public async insertWalletHistoryRecords(table: string, records: InfluxWalletType[]): Promise<void> {
    if (records.length === 0) {
      return;
    }

    const clickHouseRows = records.map((record) => convertToClickHouseRow(record, new Date()));
    await this.retryInsert(table, clickHouseRows);
  }

  private async retryInsert(table: string, rows: WalletHistoryClickHouseRow[]) {
    for (let attempt = 0; attempt < maxRetryAttempts; attempt++) {
      try {
        await this.clickHouseClient.insert({
          table,
          values: rows,
          format: 'JSONEachRow',
        });

        return;
      } catch (error) {
        logger.warn(`❗️ retryInsert: attempt ${attempt + 1} failed, retrying...`, error);
      }
    }

    throw new Error(`failed to insert clickhouse wallet_history`);
  }
}

function convertToClickHouseRow(influxObj: InfluxWalletType, event_time: Date): WalletHistoryClickHouseRow {
  return {
    partner_code: influxObj.partner_code,
    player_id: influxObj.player_id,
    action: influxObj.action,
    record_time: dayjs(influxObj._time).utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
    amount: influxObj.Amount,
    balance: influxObj.Balance,
    partner_transaction_id: influxObj.PartnerTransactionID,
    transaction_id: influxObj.TransactionID,
    event_time: dayjs(event_time).utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
  };
}
