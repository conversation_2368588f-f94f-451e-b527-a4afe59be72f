import { GameRepositoryInterface } from '../repository/gameRepository';

export class GameService {
  constructor(private gameRepository: GameRepositoryInterface) {}

  public async checkTableIsNewCreated() {
    return await this.gameRepository.checkTableIsNewCreated();
  }

  public async getAllGames() {
    return await this.gameRepository.getAllGames();
  }

  public async getGameById(id: string) {
    return await this.gameRepository.getGameById(id);
  }

  public async getGameByName(name: string) {
    return await this.gameRepository.getGameByName(name);
  }
}
