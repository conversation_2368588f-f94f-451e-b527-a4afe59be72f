import { ClickhouseRepository } from '../repository/clickhouseRepository';
import { InfluxdbSpinRecordType } from '../worker/spinHistoryWorker';
import { InfluxdbTableRecordType } from '../worker/tableHistoryWorker';
import { GameDistributionMulRecord } from '../model/spin';
export class ClickhouseService {
  constructor(private clickhouseRepository: ClickhouseRepository) {}

  public async insertSpinRecords(records: InfluxdbSpinRecordType[]) {
    return this.clickhouseRepository.insertSpinRecords(records);
  }

  public async insertTableGameRecords(records: InfluxdbTableRecordType[]) {
    return this.clickhouseRepository.insertTableGameRecords(records);
  }

  public async queryGameDistributionMul(time: Date) {
    return this.clickhouseRepository.queryGameDistributionMul(time);
  }

  public async insertGameDistributionMul(records: GameDistributionMulRecord[]) {
    return this.clickhouseRepository.insertGameDistributionMul(records);
  }
}
