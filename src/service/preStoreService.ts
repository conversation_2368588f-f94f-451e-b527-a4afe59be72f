import { RedisRepository } from '../repository/redisRepository';
import { GranularitySumUpType } from '../repository/totalSumUpRepository';

export class PreStoreService {
  constructor(private redisRepository: RedisRepository) {}

  public async storeStatisticResult(key: string, results: GranularitySumUpType[], expire: number) {
    return await this.redisRepository.storeStatisticResult(key, results, expire);
  }

  recordInconsistentTimeTickTimes = async (jobName: string) => {
    return await this.redisRepository.recordInconsistentTimeTickTimes(jobName);
  };

  public async resetInConsistentTimeTickTimes(jobName: string) {
    return await this.redisRepository.resetInConsistentTimeTickTimes(jobName);
  }
}
