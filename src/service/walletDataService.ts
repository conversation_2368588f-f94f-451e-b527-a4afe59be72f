import { <PERSON><PERSON> } from 'knex';
import { WalletDataRepository } from '../repository/walletDataRepository';
import { InfluxWalletType } from '../worker/walletHistoryWorker';

export class WalletDataService {
  constructor(private walletDataRepository: WalletDataRepository) {}

  public async checkTableIsNewCreated() {
    return await this.walletDataRepository.checkTableIsNewCreated();
  }

  public async insertWalletData(records: InfluxWalletType[], trx?: Knex.Transaction) {
    return await this.walletDataRepository.insertWalletData(records, trx);
  }

  public async transaction() {
    return await this.walletDataRepository.transaction();
  }
}
