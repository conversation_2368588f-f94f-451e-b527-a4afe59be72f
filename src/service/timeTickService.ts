import { <PERSON><PERSON> } from 'knex';
import { STATISTIC_DIRECTION } from '../handlers/jobHandler';
import { TimeTickPayload, TimeTickRepositoryInterface } from '../repository/timeTickRepository';

export class TimeTickService {
  constructor(private timeTickRepository: TimeTickRepositoryInterface) {}

  public async checkTableIsNewCreated() {
    return await this.timeTickRepository.checkTableIsNewCreated();
  }

  public async insertFirstTimeTick(timeTick: Date) {
    return await this.timeTickRepository.insertFirstTimeTick(timeTick);
  }

  public async getProcessTimeTick(tickName: string, direction: STATISTIC_DIRECTION) {
    return await this.timeTickRepository.getProcessTimeTick(tickName, direction);
  }

  public async updateProcessingTimeTick(
    taskName: string,
    timeTick: Date,
    direction: STATISTIC_DIRECTION,
    trx?: Knex.Transaction,
  ) {
    return await this.timeTickRepository.updateProcessingTimeTick(taskName, timeTick, direction, trx);
  }

  public async updateProcessedTimeTick(
    taskName: string,
    timeTick: Date,
    direction: STATISTIC_DIRECTION,
    trx?: Knex.Transaction,
  ) {
    return await this.timeTickRepository.updateProcessedTimeTick(taskName, timeTick, direction, trx);
  }

  public async insertTimeTick(payload: TimeTickPayload) {
    return await this.timeTickRepository.insertTimeTick(payload);
  }

  public async dropTable() {
    return await this.timeTickRepository.dropTimeTickTable();
  }

  public async findOneByName(name: string, options?: { fromMaster: boolean }) {
    return await this.timeTickRepository.findOneByName(name, options);
  }

  public processToSuitTimeTick(tickName: string, timeTick: Date) {
    return this.timeTickRepository.processToSuitTimeTick(tickName, timeTick);
  }
}
