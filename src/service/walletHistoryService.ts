import { WalletHistoryRepository } from '../repository/walletHistoryRepository';
import { InfluxWalletType } from '../worker/walletHistoryWorker';

export class WalletHistoryService {
  constructor(private walletHistoryRepository: WalletHistoryRepository) {}

  public async insertWalletHistoryRecords(table: string, records: InfluxWalletType[]) {
    return await this.walletHistoryRepository.insertWalletHistoryRecords(table, records);
  }
}
