import { <PERSON><PERSON> } from 'knex';
import { SumUpResult } from '../repository/influxdbRepository';
import {
  AggregateSumUpPayload,
  Granularity,
  GranularitySumUp,
  TotalSumUpRepositoryInterface,
} from '../repository/totalSumUpRepository';
import { ClickhouseTableName } from '../db';

export class TotalSumUpService {
  constructor(private totalSumUpRepository: TotalSumUpRepositoryInterface) {}

  public async checkTableIsNewCreated() {
    return await this.totalSumUpRepository.checkTableIsNewCreated();
  }

  public async upsert(
    influxSumUpResults: SumUpResult[] | GranularitySumUp[],
    granularity: Granularity,
    trx?: Knex.Transaction,
  ) {
    return await this.totalSumUpRepository.upsert(influxSumUpResults, granularity, trx);
  }

  public async aggregateSumUpByGranularity(payload: AggregateSumUpPayload, trx?: Knex.Transaction) {
    return await this.totalSumUpRepository.aggregateSumUpByGranularity(payload, trx);
  }

  public async deleteDataInRange(granularity: Granularity, startTime: Date, stopTime: Date, trx?: Knex.Transaction) {
    return await this.totalSumUpRepository.deleteDataInRange(granularity, startTime, stopTime, trx);
  }

  public async deleteDataInRangeByTableName(
    tableName: ClickhouseTableName,
    startTime: Date,
    stopTime: Date,
    trx?: Knex.Transaction,
  ) {
    return await this.totalSumUpRepository.deleteDataInRangeByTableName(tableName, startTime, stopTime, trx);
  }

  public async transaction() {
    return await this.totalSumUpRepository.transaction();
  }

  public async getSumUpByGranularity(granularity: Granularity, startTime: Date, stopTime: Date) {
    return await this.totalSumUpRepository.getSumUpByGranularity(granularity, startTime, stopTime);
  }

  public async getSumUpTotalGranularityBy(tableName: ClickhouseTableName, startTime: Date, stopTime: Date) {
    return await this.totalSumUpRepository.getSumUpTotalGranularityBy(tableName, startTime, stopTime);
  }

  public async refreshMaterializedView(granularity: Granularity) {
    return await this.totalSumUpRepository.refreshMaterializedView(granularity);
  }

  public async refreshMaterializedViewByTableNames(tableNames: ClickhouseTableName[]) {
    return await this.totalSumUpRepository.refreshMaterializedViewByTableNames(tableNames);
  }
}
