import { CountUpPayload, InfluxDbRepository, RawDataPayload, SumUpPayload } from '../repository/influxdbRepository';

interface InfluxdbServiceInterface {
  getFirstTimeTick(): Promise<Date>;
}

export class InfluxdbService implements InfluxdbServiceInterface {
  private influxdbRepository: InfluxDbRepository;

  constructor(influxdbRepository: InfluxDbRepository) {
    this.influxdbRepository = influxdbRepository;
  }

  public async getFirstTimeTick() {
    return this.influxdbRepository.getFirstTimeTick();
  }

  public async sumUp(payload: SumUpPayload) {
    return this.influxdbRepository.sumUp(payload);
  }

  public async countUp(payload: CountUpPayload) {
    return this.influxdbRepository.countUp(payload);
  }

  public async getRawData<T>(payload: RawDataPayload<T>) {
    return this.influxdbRepository.getRawData<T>(payload);
  }

  public async getTableGameRawData<T>(payload: RawDataPayload<T>) {
    return this.influxdbRepository.getRawData<T>(payload);
  }
}
