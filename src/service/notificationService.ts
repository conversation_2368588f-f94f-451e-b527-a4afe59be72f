import { ClickHouseClient } from '@clickhouse/client';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { Knex } from 'knex';
import { BigWinCollector } from '../worker/spinHistoryWorker';

dayjs.extend(utc);

export type SpinHistoryRound = {
  partner_code: string;
  player_id: string;
  game_id: string;
  round_id: string;
  round_type: string;
  bet_amount: number;
  win_amount: number;
  multiplier?: number;
  time: string;
};

export type NotificationConfig = {
  multiplier: number;
  winAmount: number;
  op: NotificationOp;
};

export type NotificationRow = Pick<SpinHistoryRound, 'win_amount' | 'bet_amount'>;

export enum NotificationName {
  Notify = 'Notify',
  Risk = 'Risk',
}

export enum NotificationOp {
  AND = 'AND',
  OR = 'OR',
}

export class NotificationService {
  private clickHouseClient: ClickHouseClient;
  private dbMaster: Knex;

  constructor(clickHouseClient: ClickHouseClient, dbMaster: Knex) {
    this.clickHouseClient = clickHouseClient;
    this.dbMaster = dbMaster;
  }

  public async getNotificationConfig(name: NotificationName): Promise<NotificationConfig> {
    const res = await this.dbMaster('app_settings')
      .select('risk_management_notify_multiplier', 'risk_management_notify_win_amount', 'risk_management_notify_op')
      .where('risk_management_notify_name', name);

    return {
      multiplier: Number(res[0].risk_management_notify_multiplier),
      winAmount: Number(res[0].risk_management_notify_win_amount),
      op: res[0].risk_management_notify_op,
    };
  }

  public async getEventsInRange(startTime: Date, endTime: Date): Promise<SpinHistoryRound[]> {
    const res = await this.clickHouseClient.query({
      query: `SELECT * from spin_history_rounds where time < toDateTime('${dayjs(endTime)
        .utc()
        .format('YYYY-MM-DD HH:mm:ss')}') AND time >= toDateTime('${dayjs(startTime)
        .utc()
        .format('YYYY-MM-DD HH:mm:ss')}');`,
      format: 'JSONEachRow',
    });

    const rows = await res.json();

    return rows.map((row: unknown) => this.convertToSpinHistoryRound(row));
  }

  public shouldSendNotification(row: BigWinCollector, notificationConfig: NotificationConfig) {
    // multiply by 10 to convert to achieve the same time with the notification win amount
    const winAmount = Number(row.win_amount) * 10;
    const betAmount = Number(row.bet_amount) * 10;
    const multiplier = row.bet_amount !== 0 ? winAmount / Number(betAmount) : -1;

    const opMap = {
      [NotificationOp.AND]: (a: boolean, b: boolean) => a && b,
      [NotificationOp.OR]: (a: boolean, b: boolean) => a || b,
    };

    return opMap[notificationConfig.op](
      notificationConfig.multiplier > 0 && multiplier >= notificationConfig.multiplier,
      notificationConfig.winAmount > 0 && winAmount >= notificationConfig.winAmount,
    );
  }

  private convertToSpinHistoryRound(row: unknown): SpinHistoryRound {
    return {
      partner_code: (row as SpinHistoryRound).partner_code as string,
      player_id: (row as SpinHistoryRound).player_id as string,
      game_id: (row as SpinHistoryRound).game_id as string,
      round_id: (row as SpinHistoryRound).round_id as string,
      round_type: (row as SpinHistoryRound).round_type as string,
      bet_amount: Number((row as SpinHistoryRound).bet_amount),
      win_amount: Number((row as SpinHistoryRound).win_amount),
      time: new Date((row as SpinHistoryRound).time).toISOString(),
    };
  }
}
