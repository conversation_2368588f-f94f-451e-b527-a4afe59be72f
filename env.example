DB_TYPE=pg
DB_URL_MASTER=postgresql://customuser:custompassword@127.0.0.1:5432/customdb

CLICKHOUSE_URL=http://localhost:8123
CLICKHOUSE_DATABASE=customdb
CLICKHOUSE_USER=customuser
CLICKHOUSE_PASSWORD=custompassword

SOURCE_INFLUXDB_URL=http://localhost:8086
SOURCE_INFLUXDB_SPIN_MEASUREMENT=spin-history
SOURCE_INFLUXDB_WALLET_MEASUREMENT=wallet-history
SOURCE_INFLUXDB_TOKEN=thisisatempass
SOURCE_INFLUXDB_ORG=edge-slot
SOURCE_INFLUXDB_BUCKET=game-data
SOURCE_INFLUXDB_CONNECTION_TIMEOUT=900000
# [{"name": "seamless-mode", "url": "http://localhost:8086"}, {"name": "wallet-mode", "url": "http://localhost:8086"}]
SOURCE_INFLUXDB_EXTENDS=""


SOURCE_INFLUXDB_TABLE_URL=http://localhost:8087
SOURCE_INFLUXDB_TABLE_MEASUREMENT=table-history
SOURCE_INFLUXDB_TABLE_WALLET_MEASUREMENT=wallet-history
SOURCE_INFLUXDB_TABLE_TOKEN=thisisatempass
SOURCE_INFLUXDB_TABLE_ORG=edge-table
SOURCE_INFLUXDB_TABLE_BUCKET=table-game-data
SOURCE_INFLUXDB_TABLE_CONNECTION_TIMEOUT=900000
# [{"name": "seamless-mode", "url": "http://localhost:8086"}, {"name": "wallet-mode", "url": "http://localhost:8086"}]
SOURCE_INFLUXDB_TABLE_EXTENDS=""

REDIS_URL=redis://localhost:6379

TIMEZONE=Asia/Jakarta

PROCESS_TO_REDIS_QUEUE_EVERY=15s

SPIN_DATA_STORE_EVERY=3s
WALLET_DATA_STORE_EVERY=15s

SPIN_DATA_TIME_RANGE=1
WALLET_HISTORY_TIME_RANGE=1440

TABLE_WALLET_DATA_STORE_EVERY=3s
TABLE_WALLET_HISTORY_TIME_RANGE=1440

GAME_DISTRIBUTION_MUL_EVERY=5s

NOTIFICATION_EVERY=10s
NOTIFICATION_TIME_RANGE=10

KAFKA_CLIENT_ID=bot-service
KAFKA_CONSUMER_GROUP_ID=bot-service
KAFKA_TOPIC_STATISTIC_SYSTEM=statistic-system
KAFKA_TOPIC_STATISTIC_RISK=statistic-risk
KAFKA_BROKERS=kafka:9092

# set the jackpot multiple notify
HEALTHY_CHECK_NOTIFY=false

MANUAL_FIRST_TIME_TICK="2023-10-01T00:00:00Z"
MAX_PROCESS_RESTART_TIME=3
DEBUG=false

DASHBOARD_URL=http://localhost:4000
