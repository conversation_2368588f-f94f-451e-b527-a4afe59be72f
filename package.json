{"name": "statistic-service", "version": "1.27.1", "main": "index.js", "scripts": {"dev": "export NODE_ENV=development && export NODE_OPTIONS='-r ts-node/register --no-warnings --loader ts-node/esm' && node src/index.ts", "start": "export NODE_ENV=production && node index.js", "build": "sh etc/build.sh", "lint": "eslint --ext .ts src", "lint:fix": "eslint --ext .ts src --fix", "prettier": "prettier --write \"src/**/*.ts\"", "prettier:check": "prettier --check \"src/**/*.ts\"", "test": "echo \"Error: no test specified\" && exit 1"}, "license": "MIT", "dependencies": {"@clickhouse/client": "^1.4.0", "@influxdata/influxdb-client": "^1.33.2", "@types/lodash": "^4.17.7", "axios": "^1.5.1", "bluebird": "^3.7.2", "bull": "^4.11.3", "dayjs": "^1.11.9", "dotenv": "^16.3.1", "express": "^4.18.2", "ioredis": "^5.3.2", "kafkajs": "^2.2.4", "knex": "^2.5.1", "lodash": "^4.17.21", "lru-cache": "^11.0.2", "ms": "^2.1.3", "pg": "^8.11.3", "redis": "^4.6.12", "rimraf": "^5.0.1", "snowflake-id": "^1.1.0", "uuid": "^10.0.0", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/bull": "^4.10.0", "@types/express": "^4.17.18", "@types/ms": "^0.7.34", "@types/node": "^20.5.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.6.0", "@typescript-eslint/parser": "^6.6.0", "eslint": "^8.0.1", "eslint-config-prettier": "^9.0.0", "eslint-config-standard-with-typescript": "^39.0.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0 || ^16.0.0 ", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-promise": "^6.0.0", "prettier": "^3.0.3", "ts-node": "^10.9.1", "tslib": "^2.6.2", "typescript": "^5.2.2"}}