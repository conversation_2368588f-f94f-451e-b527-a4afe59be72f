/* eslint-disable @typescript-eslint/no-var-requires */
const dotenv = require('dotenv');
const BPromise = require('bluebird');
const { spawn } = require('child_process');
const SnowFlakeId = require('snowflake-id').default;
dotenv.config();
const { InfluxDB, Point } = require('@influxdata/influxdb-client');
var snowflake = new SnowFlakeId({
  mid: 42,
  offset: (2023 - 1970) * 31536000 * 1000,
});

const betAmountList = [10, 20, 50, 100, 200, 500, 1000];
const MAX_RECORD_COUNT = process.env.MAX_RECORD_COUNT || 1000;

const games = (process.env.GAME_CODES || '').split(',');
const partners = (process.env.PARTNER_CODES || '').split(',');
const playerPerPartner = Number(process.env.PLAYERS_PER_PARTNER) || 1000;
const players = Array.from(new Array(playerPerPartner), (_, idx) => `player-${idx + 1}`);
console.log('🚀 ~ file: game-data-generator.js:16 ~ playerPerPartner:', playerPerPartner);
const measurement = process.env.INFLUXDB_SPIN_MEASUREMENT || 'spin-history';
const org = process.env.INFLUXDB_ORG || '';
const bucket = process.env.INFLUXDB_BUCKET || '';

const token = process.env.INFLUXDB_TOKEN || '';

const url = process.env.INFLUXDB_URL || 'http://localhost:8086';

const randomGame = () => games[~~(Math.random() * games.length)];
const randomBetAmount = () => betAmountList[~~(Math.random() * betAmountList.length)];

const generateSpinHistory = async (client, partner, player) => {
  const writeApi = client.getWriteApi(org, bucket, 'ms');

  // setup default tags for all writes through this API
  writeApi.useDefaultTags({ location: 'localhost' });

  const recordPerPlayer = ~~(MAX_RECORD_COUNT / (playerPerPartner * partners.length));
  const timeTick = new Date().getTime();
  let spinRecords = Array.from(new Array(recordPerPlayer), (_, idx) => {
    const spinTime = timeTick + idx * 1000;
    const spinType = Math.floor(Math.random() * 2) === 0 ? 'win' : 'lose';
    const betAmount = randomBetAmount();
    const winAmount = randomBetAmount();
    const randomPlayerBalance = Math.floor(Math.random() * 1000000);

    return new Point(measurement)
      .tag('partner_code', partner)
      .tag('player_id', player)
      .tag('game_id', randomGame())
      .tag('round_type', spinType)
      .stringField('SpinID', snowflake.generate())
      .stringField('RoundID', snowflake.generate())
      .intField('BetAmount', betAmount)
      .intField('WinAmount', winAmount)
      .booleanField('IsFreeRounds', false)
      .booleanField('IsFreeSpins', false)
      .stringField('State', '{}')
      .intField('BalanceBefore', randomPlayerBalance + betAmount - winAmount)
      .intField('BalanceAfter', randomPlayerBalance)
      .timestamp(spinTime);
  });

  const MAX_LENGTH_PER_WRITE = 1000;

  try {
    console.log(
      `Generated ${spinRecords.length} records for ${player} in ${partner} (${new Date(
        timeTick + recordPerPlayer * 1000,
      ).toISOString()} - ${new Date(timeTick).toISOString()})`,
    );

    for (let start = 0; start < spinRecords.length; start += MAX_LENGTH_PER_WRITE) {
      const end = Math.min(start + MAX_LENGTH_PER_WRITE, spinRecords.length);
      const spinRecordsChunk = spinRecords.slice(start, end);
      console.log(`  > Write ${start}:${end}(${spinRecordsChunk.length}) into records for ${player} in ${partner}`);
      await BPromise.delay(100 + ~~(Math.random() * 1000));
      await writeApi.writePoints(spinRecordsChunk);
      // console.log(`Generated ${spinRecords.length} records for ${player} in ${partner}`, spinRecords[i]);
    }

    console.log(`Write ${spinRecords.length} into records for ${player} in ${partner}`);
  } catch (error) {
    console.error(error);
  }

  await writeApi.close();
};

const main = async () => {
  if (!process.env.PARTNER_CODE) {
    // means this is parent process

    const promises = partners.map((partner) => {
      return new Promise((resolve) => {
        const child = spawn('node', ['game-data-generator.js'], {
          env: {
            ...process.env,
            PARTNER_CODE: partner,
          },
          stdio: 'inherit',
        });

        child.stdout?.on('data', (m) => {
          console.log(`PARENT got message ${partner} :`, m);
        });
        child.stderr?.on('data', (m) => {
          console.log(`PARENT got message ${partner} :`, m);
        });
        child?.on('close', (code) => {
          console.log(`child process exist with ${code}`);
          resolve(code ? 'error' : 'success');
        });
      });
    });

    await Promise.all(promises.flat());

    return;
  }

  const client = new InfluxDB({ url, token, timeout: 20000 });

  const partner = process.env.PARTNER_CODE;

  for (let i = 0; i < players.length; i++) {
    const player = players[i];
    console.log(
      '🚀 ~ file: game-data-generator.js:123 ~ main ~ partner:',
      partner,
      'player:',
      player,
      `(${new Date().toISOString()})`,
    );
    await generateSpinHistory(client, partner, player);
    console.log(
      '🚀 ~ file: game-data-generator.js:125 ~ main ~ partner:',
      partner,
      'player:',
      player,
      `(${new Date().toISOString()})`,
      'success',
    );
  }

  // const result = await Promise.all(spinRecordPromise);

  // const result = await generateSpinHistory(client, 'partner1', 'player-1');

  // console.log("🚀 ~ file: game-data-generator.js:80 ~ main ~ result:", result.length)
};

main();
