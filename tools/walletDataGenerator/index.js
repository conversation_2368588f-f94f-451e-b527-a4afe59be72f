/* eslint-disable @typescript-eslint/no-var-requires */
require('dotenv').config();
const { InfluxDB, Point } = require('@influxdata/influxdb-client');

// InfluxDB 配置
const url = process.env.INFLUXDB_URL || 'http://**************:8086';
const token = process.env.INFLUXDB_TOKEN || 'thisisatempass';
const org = process.env.INFLUXDB_ORG || 'edge-slot';
const bucket = process.env.INFLUXDB_BUCKET || 'game-data';
const measurement = process.env.INFLUXDB_MEASUREMENT || 'wallet-history';

// 创建 InfluxDB 客户端
const influxDB = new InfluxDB({ url, token });
const writeApi = influxDB.getWriteApi(org, bucket);

// 修改模拟参数
const SIMULATION_CONFIG = {
  PLAYERS_ONLINE: 100, // 同时在线100个玩家
  AVG_BET_INTERVAL: 3000, // 平均每个玩家3秒下一次注
  RESULT_DELAY_MIN: 500, // 最小结果延迟（毫秒）
  RESULT_DELAY_MAX: 2000, // 最大结果延迟（毫秒）
  WIN_RATE: 0.7, // 胜率70%
  MIN_BET: 100, // 最小下注金额
  MAX_BET: 1000, // 最大下注金额
};

// 生成基础随机数据
function generateBaseData() {
  const actionList = ['Deposit', 'Withdraw', 'Reward'];
  return {
    partnerCode: `partner_code_${Math.floor(Math.random() * 5)}`,
    gameId: `game_id_${Math.floor(Math.random() * 100)}`,
    playerId: `player_id_${Math.floor(Math.random() * 1000)}`,
    action: actionList[Math.floor(Math.random() * actionList.length)],
    roundId: (Date.now() + 1000).toString(),
  };
}

// 修改写入函数，添加计数
function writeGameEvent(baseData, betAmount = 0, winAmount = 0, balanceBefore = 10000) {
  const randomTransactionId = (515776790984130561n + BigInt(new Date().getTime())).toString();
  const point = new Point(measurement)
    .tag('partner_code', baseData.partnerCode)
    .tag('player_id', baseData.playerId)
    .tag('action', baseData.action)
    .stringField('Amount', betAmount.toString())
    .stringField('Balance', balanceBefore.toString())
    .stringField('PartnerTransactionID', `CP_2023090416104191471460V2OQR${new Date().getTime()}M`)
    .stringField('TransactionID', randomTransactionId);

  writeApi.writePoint(point);
  recordCount++;
  return balanceBefore - betAmount + winAmount;
}

// 修改主循环：模拟100人在线
function startSimulation() {
  // 计算触发间隔
  // 100人每3秒下注一次，意味着每秒约33次操作
  // 每次操作产生2条记录（bet + win），所以每秒约66条记录
  const interval = Math.floor(SIMULATION_CONFIG.AVG_BET_INTERVAL / SIMULATION_CONFIG.PLAYERS_ONLINE);

  console.log(`Simulation configured to generate approximately:
    - ${Math.floor(1000 / interval)} bets per second
    - ${Math.floor((1000 / interval) * 2)} records per second
    - ${Math.floor((1000 / interval) * 2 * 60)} records per minute
    - ${Math.floor((1000 / interval) * 2 * 60 * 60)} records per hour
    - ${Math.floor((1000 / interval) * 2 * 60 * 60 * 24)} records per day
  `);

  setInterval(() => {
    simulateGameRound();
  }, interval);
}

// 修改游戏回合模拟
function simulateGameRound() {
  const baseData = generateBaseData();
  const betAmount =
    Math.floor(Math.random() * (SIMULATION_CONFIG.MAX_BET - SIMULATION_CONFIG.MIN_BET + 1)) + SIMULATION_CONFIG.MIN_BET;
  const balanceBefore = 10000;

  // 写入下注记录
  writeGameEvent(baseData, betAmount, 0, balanceBefore);

  // 模拟游戏结果延迟
  const delay =
    Math.random() * (SIMULATION_CONFIG.RESULT_DELAY_MAX - SIMULATION_CONFIG.RESULT_DELAY_MIN) +
    SIMULATION_CONFIG.RESULT_DELAY_MIN;

  setTimeout(() => {
    const isWin = Math.random() < SIMULATION_CONFIG.WIN_RATE;
    const winAmount = isWin ? betAmount * (Math.random() * 2 + 1) : 0;
    writeGameEvent(baseData, 0, winAmount, balanceBefore - betAmount);
  }, delay);
}

// 添加定时统计
let recordCount = 0;
setInterval(() => {
  console.log(`Generated ${recordCount} records in the last minute`);
  recordCount = 0;
}, 60000);

// 优雅退出
process.on('SIGINT', async () => {
  try {
    await writeApi.close();
    console.log('InfluxDB connection closed');
    process.exit(0);
  } catch (error) {
    console.error('Error closing InfluxDB connection:', error);
    process.exit(1);
  }
});

// 启动模拟
console.log('Starting simulation with ~100 players...');
startSimulation();
